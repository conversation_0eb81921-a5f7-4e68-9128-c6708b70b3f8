"""
Notification system for the Real Estate Monitor application.
Handles web notifications, desktop notifications, and WebSocket integration.
"""
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum

try:
    from plyer import notification as desktop_notification
    DESKTOP_NOTIFICATIONS_AVAILABLE = True
except ImportError:
    DESKTOP_NOTIFICATIONS_AVAILABLE = False
    desktop_notification = None

from shared.models import PropertyListing


class NotificationType(Enum):
    """Types of notifications that can be sent"""
    PROPERTY_FOUND = "property_found"
    PROPERTY_REMOVED = "property_removed"
    STATUS_UPDATE = "status_update"
    ERROR = "error"


@dataclass
class NotificationMessage:
    """Represents a notification message"""
    notification_type: NotificationType
    title: str
    message: str
    timestamp: datetime
    property_listing: Optional[PropertyListing] = None
    data: Optional[Dict[str, Any]] = None


class NotificationManager:
    """
    Manages all types of notifications for the Real Estate Monitor application.
    Supports web notifications, desktop notifications, and WebSocket integration.
    """
    
    def __init__(self, app_name: str = "Real Estate Monitor"):
        """
        Initialize the notification manager.
        
        Args:
            app_name: Name of the application for notifications
        """
        self.app_name = app_name
        self.logger = logging.getLogger(__name__)
        
        # WebSocket callbacks for real-time updates
        self._websocket_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        
        # Notification settings
        self.desktop_notifications_enabled = DESKTOP_NOTIFICATIONS_AVAILABLE
        self.web_notifications_enabled = True
        
        # Check desktop notification availability
        if not DESKTOP_NOTIFICATIONS_AVAILABLE:
            self.logger.warning("Desktop notifications not available - plyer library not installed")
    
    def add_websocket_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Add a WebSocket callback for real-time notifications.
        
        Args:
            callback: Function that takes notification data and sends it via WebSocket
        """
        if callback not in self._websocket_callbacks:
            self._websocket_callbacks.append(callback)
    
    def remove_websocket_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Remove a WebSocket callback.
        
        Args:
            callback: Callback function to remove
        """
        if callback in self._websocket_callbacks:
            self._websocket_callbacks.remove(callback)
    
    def notify_property_found(self, listing: PropertyListing) -> None:
        """
        Send notification when a target property is found.
        
        Args:
            listing: PropertyListing that was found
        """
        try:
            # Format the notification message
            title = "🏠 Target Property Found!"
            message = self._format_property_found_message(listing)
            
            notification_msg = NotificationMessage(
                notification_type=NotificationType.PROPERTY_FOUND,
                title=title,
                message=message,
                timestamp=datetime.now(),
                property_listing=listing
            )
            
            # Send notifications through all channels
            self._send_notification(notification_msg)
            
            self.logger.info(f"Property found notification sent for: {listing.address}")
            
        except Exception as e:
            self.logger.error(f"Error sending property found notification: {e}")
    
    def notify_property_removed(self, mls_number: str, address: Optional[str] = None) -> None:
        """
        Send notification when a target property is removed from listings.
        
        Args:
            mls_number: MLS number of the removed property
            address: Address of the removed property (if available)
        """
        try:
            # Format the notification message
            title = "🏠 Target Property Removed"
            message = self._format_property_removed_message(mls_number, address)
            
            notification_msg = NotificationMessage(
                notification_type=NotificationType.PROPERTY_REMOVED,
                title=title,
                message=message,
                timestamp=datetime.now(),
                data={"mls_number": mls_number, "address": address}
            )
            
            # Send notifications through all channels
            self._send_notification(notification_msg)
            
            self.logger.info(f"Property removed notification sent for MLS: {mls_number}")
            
        except Exception as e:
            self.logger.error(f"Error sending property removed notification: {e}")
    
    def notify_status_update(self, message: str, status_type: str = "info") -> None:
        """
        Send a status update notification.
        
        Args:
            message: Status message to send
            status_type: Type of status (info, warning, error)
        """
        try:
            # Format the notification message
            title = self._get_status_title(status_type)
            
            notification_msg = NotificationMessage(
                notification_type=NotificationType.STATUS_UPDATE,
                title=title,
                message=message,
                timestamp=datetime.now(),
                data={"status_type": status_type}
            )
            
            # Send notifications through all channels (desktop only for important status)
            self._send_notification(notification_msg, desktop_only_for_status=True)
            
            self.logger.info(f"Status notification sent: {message}")
            
        except Exception as e:
            self.logger.error(f"Error sending status notification: {e}")
    
    def notify_error(self, error_message: str, error: Optional[Exception] = None) -> None:
        """
        Send an error notification.
        
        Args:
            error_message: Error message to send
            error: Optional exception object
        """
        try:
            # Format the notification message
            title = "⚠️ Monitoring Error"
            message = self._format_error_message(error_message, error)
            
            notification_msg = NotificationMessage(
                notification_type=NotificationType.ERROR,
                title=title,
                message=message,
                timestamp=datetime.now(),
                data={"error_message": error_message, "error_type": type(error).__name__ if error else None}
            )
            
            # Send notifications through all channels
            self._send_notification(notification_msg)
            
            self.logger.error(f"Error notification sent: {error_message}")
            
        except Exception as e:
            self.logger.error(f"Error sending error notification: {e}")
    
    def _send_notification(self, notification: NotificationMessage, desktop_only_for_status: bool = False) -> None:
        """
        Send notification through all enabled channels.
        
        Args:
            notification: NotificationMessage to send
            desktop_only_for_status: If True, only send desktop notifications for status updates
        """
        # Send WebSocket notification for real-time updates
        if self.web_notifications_enabled and not desktop_only_for_status:
            self._send_websocket_notification(notification)
        
        # Send desktop notification
        if self.desktop_notifications_enabled:
            self._send_desktop_notification(notification)
    
    def _send_websocket_notification(self, notification: NotificationMessage) -> None:
        """
        Send notification via WebSocket to connected clients.
        
        Args:
            notification: NotificationMessage to send
        """
        try:
            # Convert notification to WebSocket message format
            websocket_data = {
                "type": "notification",
                "notification_type": notification.notification_type.value,
                "title": notification.title,
                "message": notification.message,
                "timestamp": notification.timestamp.isoformat(),
                "data": notification.data or {}
            }
            
            # Add property data if available
            if notification.property_listing:
                websocket_data["property"] = {
                    "address": notification.property_listing.address,
                    "price": notification.property_listing.price,
                    "mls_number": notification.property_listing.mls_number,
                    "description": notification.property_listing.description,
                    "square_footage": notification.property_listing.square_footage,
                    "bedrooms": notification.property_listing.bedrooms,
                    "bathrooms": notification.property_listing.bathrooms,
                    "lot_size": notification.property_listing.lot_size,
                    "year_built": notification.property_listing.year_built,
                    "image_url": notification.property_listing.image_url,
                    "listing_url": notification.property_listing.listing_url,
                    "first_seen": notification.property_listing.first_seen.isoformat()
                }
            
            # Send to all registered WebSocket callbacks
            for callback in self._websocket_callbacks:
                try:
                    callback(websocket_data)
                except Exception as e:
                    self.logger.error(f"Error in WebSocket callback: {e}")
            
        except Exception as e:
            self.logger.error(f"Error sending WebSocket notification: {e}")
    
    def _send_desktop_notification(self, notification: NotificationMessage) -> None:
        """
        Send desktop notification using plyer library.
        
        Args:
            notification: NotificationMessage to send
        """
        if not DESKTOP_NOTIFICATIONS_AVAILABLE:
            return
        
        try:
            # Truncate message for desktop notifications (they have length limits)
            desktop_message = notification.message
            if len(desktop_message) > 200:
                desktop_message = desktop_message[:197] + "..."
            
            # Send desktop notification
            desktop_notification.notify(
                title=notification.title,
                message=desktop_message,
                app_name=self.app_name,
                timeout=10  # Show for 10 seconds
            )
            
        except Exception as e:
            self.logger.error(f"Error sending desktop notification: {e}")
    
    def _format_property_found_message(self, listing: PropertyListing) -> str:
        """
        Format a property found notification message.
        
        Args:
            listing: PropertyListing that was found
            
        Returns:
            Formatted notification message
        """
        message_parts = [
            f"Address: {listing.address}",
            f"Price: {listing.price}",
            f"MLS: {listing.mls_number}"
        ]
        
        # Add additional details if available
        if listing.bedrooms is not None:
            message_parts.append(f"Bedrooms: {listing.bedrooms}")
        
        if listing.bathrooms is not None:
            message_parts.append(f"Bathrooms: {listing.bathrooms}")
        
        if listing.square_footage:
            message_parts.append(f"Size: {listing.square_footage}")
        
        # Add description preview (first 100 characters)
        if listing.description:
            description_preview = listing.description[:100]
            if len(listing.description) > 100:
                description_preview += "..."
            message_parts.append(f"Description: {description_preview}")
        
        return "\n".join(message_parts)
    
    def _format_property_removed_message(self, mls_number: str, address: Optional[str] = None) -> str:
        """
        Format a property removed notification message.
        
        Args:
            mls_number: MLS number of the removed property
            address: Address of the removed property (if available)
            
        Returns:
            Formatted notification message
        """
        if address:
            return f"The target property at {address} (MLS: {mls_number}) has been removed from listings."
        else:
            return f"A target property (MLS: {mls_number}) has been removed from listings."
    
    def _format_error_message(self, error_message: str, error: Optional[Exception] = None) -> str:
        """
        Format an error notification message.
        
        Args:
            error_message: Error message
            error: Optional exception object
            
        Returns:
            Formatted error message
        """
        if error:
            return f"{error_message}\nError: {str(error)}"
        else:
            return error_message
    
    def _get_status_title(self, status_type: str) -> str:
        """
        Get appropriate title for status notifications.
        
        Args:
            status_type: Type of status (info, warning, error)
            
        Returns:
            Appropriate title for the status type
        """
        status_titles = {
            "info": "ℹ️ Status Update",
            "warning": "⚠️ Warning",
            "error": "❌ Error",
            "success": "✅ Success"
        }
        
        return status_titles.get(status_type, "📢 Notification")
    
    def set_desktop_notifications_enabled(self, enabled: bool) -> None:
        """
        Enable or disable desktop notifications.
        
        Args:
            enabled: Whether to enable desktop notifications
        """
        if enabled and not DESKTOP_NOTIFICATIONS_AVAILABLE:
            self.logger.warning("Cannot enable desktop notifications - plyer library not available")
            return
        
        self.desktop_notifications_enabled = enabled
        self.logger.info(f"Desktop notifications {'enabled' if enabled else 'disabled'}")
    
    def set_web_notifications_enabled(self, enabled: bool) -> None:
        """
        Enable or disable web notifications.
        
        Args:
            enabled: Whether to enable web notifications
        """
        self.web_notifications_enabled = enabled
        self.logger.info(f"Web notifications {'enabled' if enabled else 'disabled'}")
    
    def is_desktop_notifications_available(self) -> bool:
        """
        Check if desktop notifications are available.
        
        Returns:
            True if desktop notifications are available, False otherwise
        """
        return DESKTOP_NOTIFICATIONS_AVAILABLE
    
    def get_notification_settings(self) -> Dict[str, Any]:
        """
        Get current notification settings.
        
        Returns:
            Dictionary with current notification settings
        """
        return {
            "desktop_notifications_enabled": self.desktop_notifications_enabled,
            "desktop_notifications_available": DESKTOP_NOTIFICATIONS_AVAILABLE,
            "web_notifications_enabled": self.web_notifications_enabled,
            "app_name": self.app_name
        }