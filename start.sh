#!/bin/bash
# Real Estate Monitor - Unix/Linux/macOS Startup Script
# This script starts the Real Estate Monitor application

set -e  # Exit on any error

echo "Starting Real Estate Monitor..."
echo

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH"
    echo "Please install Python 3.8 or higher"
    exit 1
fi

# Use python3 if available, otherwise python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1-2)
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "Error: Python $REQUIRED_VERSION or higher is required, found $PYTHON_VERSION"
    exit 1
fi

# Check if requirements are installed
if ! $PYTHON_CMD -c "import flask" &> /dev/null; then
    echo "Installing required dependencies..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

# Start the application
echo
echo "Starting Real Estate Monitor application..."
$PYTHON_CMD main.py "$@"