#!/usr/bin/env python3
"""
Integration test script for the MeadowNorthScraper.
This script demonstrates how to use the scraper with mock data.
"""

from backend.scraper import MeadowNorthScraper
from shared.models import PropertyListing

def test_scraper_with_mock_data():
    """Test the scraper with mock HTML data"""
    
    # Create scraper instance
    scraper = MeadowNorthScraper()
    
    # Mock HTML that simulates meadownorth.ca structure
    mock_html = """
    <html>
        <body>
            <div class="listing-card">
                <div class="price">$450,000</div>
                <div class="address">123 Main Street</div>
                <div class="mls">MLS# 12345</div>
                <div class="description">Beautiful 3 bedroom home with modern updates and great location</div>
                <div class="details">3 bed, 2 bath, 1,500 sq ft</div>
                <div class="lot">0.25 acres</div>
                <div class="year">Built 2010</div>
                <img src="/images/property1.jpg" alt="Property image">
                <a href="/listing/12345">View Details</a>
            </div>
            <div class="listing-card">
                <div class="price">$325,000</div>
                <div class="address">456 Oak Avenue</div>
                <div class="mls">MLS# 67890</div>
                <div class="description">Cozy starter home in quiet neighborhood</div>
                <div class="details">2 bed, 1 bath, 1,200 sq ft</div>
                <img src="https://example.com/image2.jpg" alt="Property image">
                <a href="https://meadownorth.ca/listing/67890">View Details</a>
            </div>
        </body>
    </html>
    """
    
    print("Testing MeadowNorthScraper with mock data...")
    
    try:
        # Parse the mock HTML
        listings = scraper.parse_listing_html(mock_html)
        
        print(f"\nFound {len(listings)} property listings:")
        print("-" * 50)
        
        for i, listing in enumerate(listings, 1):
            print(f"\nListing {i}:")
            print(f"  Address: {listing.address}")
            print(f"  Price: {listing.price}")
            print(f"  MLS: {listing.mls_number}")
            print(f"  Bedrooms: {listing.bedrooms}")
            print(f"  Bathrooms: {listing.bathrooms}")
            print(f"  Square Footage: {listing.square_footage}")
            print(f"  Lot Size: {listing.lot_size}")
            print(f"  Year Built: {listing.year_built}")
            print(f"  Image URL: {listing.image_url}")
            print(f"  Listing URL: {listing.listing_url}")
            print(f"  Description: {listing.description[:100]}...")
        
        print("\n" + "=" * 50)
        print("✅ Scraper test completed successfully!")
        
        # Verify we got the expected data
        assert len(listings) == 2
        assert listings[0].address == "123 Main Street"
        assert listings[0].price == "$450,000"
        assert listings[0].bedrooms == 3
        assert listings[1].address == "456 Oak Avenue"
        assert listings[1].price == "$325,000"
        assert listings[1].bedrooms == 2
        
        print("✅ All assertions passed!")
        
    except Exception as e:
        print(f"❌ Error during scraper test: {str(e)}")
        raise

if __name__ == "__main__":
    test_scraper_with_mock_data()