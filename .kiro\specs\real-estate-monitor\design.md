# Design Document

## Overview

The Real Estate Monitor is a web-based application with a Python backend that continuously monitors meadownorth.ca for a specific address configured by the user. The system features a modern, minimalist web interface with real-time updates, uses web scraping to fetch listing data, implements fuzzy address matching to handle variations, and provides both web and desktop notifications when the target property appears or disappears from listings.

## Architecture

The application follows a modular architecture with clear separation of concerns:

```mermaid
graph TD
    A[Web Frontend] --> B[Flask Backend]
    B --> C[Configuration Manager]
    B --> D[Web Scraper]
    B --> E[Address Matcher]
    B --> F[Notification System]
    B --> G[Data Storage]
    
    A --> H[Real-time Updates via WebSocket]
    D --> I[HTTP Client]
    D --> J[HTML Parser]
    E --> K[Fuzzy Matching]
    F --> L[Web Notifications]
    F --> M[Desktop Notifications]
    G --> N[JSON File Storage]
```

### Core Components:

1. **Web Frontend**: Modern, responsive web interface with real-time updates
2. **Flask Backend**: RESTful API server that orchestrates monitoring and serves the frontend
3. **Configuration Manager**: Handles user settings and target address configuration
4. **Web Scraper**: Fetches and parses listing data from meadownorth.ca
5. **Address Matcher**: Implements fuzzy matching logic for address comparison
6. **Notification System**: Handles web and desktop alerts when target property is found
7. **Data Storage**: Persists application state and listing history
8. **WebSocket Handler**: Provides real-time updates to the frontend

## Frontend Design

### Design Philosophy
A modern, minimalist interface with a focus on clarity and real-time information display. The design uses a real estate-inspired color palette with professional aesthetics.

### Color Palette
- **Primary**: Deep Teal (#0D7377)
- **Secondary**: Warm Gold (#F4A460)
- **Success**: Emerald Green (#10B981)
- **Alert**: Amber (#F59E0B)
- **Background**: Off-White (#FAFAF9)
- **Dark Mode Background**: Charcoal (#1A1A1A)
- **Text Primary**: Dark Gray (#1F2937)
- **Text Secondary**: Medium Gray (#6B7280)

### Typography
- **Headings**: Inter or SF Pro Display (Modern, Clean)
- **Body**: Inter or SF Pro Text
- **Monospace**: JetBrains Mono (for addresses, technical info)

### Key UI Components
1. **Main Dashboard**: Shows monitoring status, target address, and found properties
2. **Property Cards**: Display property details with images and key information
3. **Configuration Panel**: Slide-over panel for settings and address configuration
4. **Status Indicators**: Real-time monitoring status with color-coded states
5. **Activity Feed**: Recent monitoring events and status changes
6. **Notification Toasts**: In-app alerts for property matches

### Responsive Design
- Desktop-first approach with mobile breakpoints
- Touch-friendly interface elements
- Optimized layouts for tablets and phones

## Components and Interfaces

### Flask Backend Routes
```python
@app.route('/api/config', methods=['GET', 'POST'])
@app.route('/api/status')
@app.route('/api/listings')
@app.route('/api/start-monitoring', methods=['POST'])
@app.route('/api/stop-monitoring', methods=['POST'])
@app.route('/api/check-now', methods=['POST'])
```

### Configuration Manager
```python
class ConfigManager:
    def load_config() -> dict
    def save_config(config: dict) -> None
    def get_target_address() -> str
    def set_target_address(address: str) -> None
    def get_check_interval() -> int
```

**Responsibilities:**
- Load/save configuration from JSON file
- Validate target address format
- Manage monitoring interval settings
- Handle first-time setup prompts

### Web Scraper
```python
class MeadowNorthScraper:
    def fetch_listings() -> List[PropertyListing]
    def parse_listing_html(html: str) -> List[PropertyListing]
    def extract_property_data(element) -> PropertyListing
```

**Responsibilities:**
- HTTP requests to meadownorth.ca/listings.html
- Parse HTML structure to extract property data
- Handle network errors and retries
- Normalize extracted data

### Address Matcher
```python
class AddressMatcher:
    def normalize_address(address: str) -> str
    def calculate_similarity(addr1: str, addr2: str) -> float
    def find_matches(target: str, listings: List[PropertyListing]) -> List[PropertyListing]
```

**Responsibilities:**
- Normalize addresses (remove extra spaces, standardize abbreviations)
- Implement fuzzy string matching using difflib
- Handle common address variations (ST/STREET, AVE/AVENUE)
- Return similarity scores for ranking matches

### Notification System
```python
class NotificationManager:
    def notify_property_found(listing: PropertyListing) -> None
    def notify_property_removed(listing: PropertyListing) -> None
    def notify_status(message: str) -> None
```

**Responsibilities:**
- Console output with colored text
- Desktop notifications (using plyer library)
- Format property information for display
- Log important events

## Data Models

### PropertyListing
```python
@dataclass
class PropertyListing:
    address: str
    price: str
    mls_number: str
    description: str
    square_footage: Optional[str]
    bedrooms: Optional[int]
    bathrooms: Optional[int]
    lot_size: Optional[str]
    year_built: Optional[str]
    image_url: str
    listing_url: str
    first_seen: datetime
```

### Configuration
```python
@dataclass
class Config:
    target_address: str
    check_interval_minutes: int = 15
    notification_enabled: bool = True
    last_check: Optional[datetime] = None
    found_listings: List[str] = field(default_factory=list)
```

## Error Handling

### Network Errors
- Implement exponential backoff for failed requests
- Retry up to 3 times before logging error
- Continue monitoring even if individual checks fail
- Log all network issues with timestamps

### Parsing Errors
- Handle changes in website structure gracefully
- Log parsing failures with HTML snippets for debugging
- Continue operation with partial data when possible
- Alert user if parsing consistently fails

### Configuration Errors
- Validate address format on input
- Provide helpful error messages for invalid configurations
- Create default config file if missing
- Handle file permission issues

## Testing Strategy

### Unit Tests
- **ConfigManager**: Test config loading/saving, validation
- **AddressMatcher**: Test normalization and fuzzy matching algorithms
- **MeadowNorthScraper**: Test HTML parsing with mock data
- **NotificationManager**: Test message formatting and delivery

### Integration Tests
- **End-to-End Monitoring**: Test complete monitoring cycle with mock website
- **Address Matching**: Test with real address variations from the site
- **Error Recovery**: Test behavior during network failures

### Manual Testing
- **Real Website Testing**: Verify scraping works with current site structure
- **Address Variations**: Test with different address formats
- **Long-Running Monitoring**: Verify stability over extended periods

## Implementation Details

### Web Scraping Approach
- Use `requests` library for HTTP requests
- Use `BeautifulSoup` for HTML parsing
- Implement rate limiting to be respectful to the website
- Cache results to avoid unnecessary requests

### Address Matching Algorithm
1. Normalize both target and listing addresses:
   - Convert to uppercase
   - Remove extra whitespace
   - Standardize street type abbreviations
2. Calculate similarity using `difflib.SequenceMatcher`
3. Consider match if similarity > 0.8 or exact substring match
4. Handle special cases like apartment numbers

### Monitoring Loop
1. Load configuration and validate target address
2. Fetch current listings from website
3. Search for target address in listings
4. Compare with previous results to detect changes
5. Send notifications for new/removed listings
6. Wait for configured interval before next check
7. Handle interruptions gracefully (Ctrl+C)

### Data Persistence
- Store configuration in `config.json`
- Store listing history in `listings_history.json`
- Create data directory if it doesn't exist
- Handle file corruption gracefully

### Dependencies

#### Backend
- `flask`: Web framework for API server
- `flask-socketio`: WebSocket support for real-time updates
- `requests`: HTTP client for web scraping
- `beautifulsoup4`: HTML parsing
- `plyer`: Cross-platform desktop notifications
- `difflib`: Built-in fuzzy string matching
- `dataclasses`: Data structure definitions
- `json`: Configuration and data persistence
- `datetime`: Timestamp handling
- `threading`: Background monitoring tasks
- `logging`: Error and debug logging

#### Frontend
- **HTML5/CSS3**: Modern web standards
- **JavaScript (ES6+)**: Interactive functionality
- **WebSocket API**: Real-time communication
- **CSS Grid/Flexbox**: Responsive layouts
- **CSS Custom Properties**: Theme system
- **Fetch API**: HTTP requests to backend