"""
Test the logging configuration to ensure it works correctly.
"""
from shared.logging_config import setup_logging, get_logger


def test_logging():
    """Test basic logging functionality"""
    # Set up logging
    logger = setup_logging(log_level="INFO", log_to_file=True)
    
    # Test different log levels
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test getting a module-specific logger
    module_logger = get_logger("test_module")
    module_logger.info("This is a message from a module-specific logger")
    
    print("Logging test completed successfully!")


if __name__ == "__main__":
    test_logging()