#!/usr/bin/env python3
"""
Complete startup and shutdown test for the Real Estate Monitor application.
This test verifies the entire application lifecycle.
"""

import sys
import os
import threading
import time
import requests
from unittest.mock import patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_startup_shutdown():
    """Test complete application startup and shutdown cycle"""
    print("="*60)
    print("  REAL ESTATE MONITOR - COMPLETE STARTUP TEST")
    print("="*60)
    
    try:
        from main import RealEstateMonitorApp
        
        # Use a different port to avoid conflicts
        test_port = 5001
        app = RealEstateMonitorApp(host='127.0.0.1', port=test_port, debug=False, no_browser=True)
        
        print(f"Starting application on port {test_port}...")
        
        # Start the application in a separate thread
        app_thread = threading.Thread(target=app.start, daemon=True)
        app_thread.start()
        
        # Wait for the server to start
        print("Waiting for server to start...")
        max_attempts = 20
        server_started = False
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"http://127.0.0.1:{test_port}/api/health", timeout=2)
                if response.status_code in [200, 503]:  # 503 is acceptable for degraded health
                    server_started = True
                    print(f"✓ Server started successfully (attempt {attempt + 1})")
                    break
            except requests.exceptions.RequestException:
                if attempt < max_attempts - 1:
                    time.sleep(1)
                    continue
        
        if not server_started:
            print("✗ Server failed to start within timeout")
            return False
        
        # Test API endpoints
        print("Testing API endpoints...")
        
        # Test health endpoint
        try:
            response = requests.get(f"http://127.0.0.1:{test_port}/api/health", timeout=5)
            print(f"✓ Health endpoint responded with status {response.status_code}")
        except Exception as e:
            print(f"✗ Health endpoint failed: {e}")
            return False
        
        # Test config endpoint
        try:
            response = requests.get(f"http://127.0.0.1:{test_port}/api/config", timeout=5)
            if response.status_code == 200:
                print("✓ Config endpoint responded successfully")
            else:
                print(f"✗ Config endpoint returned status {response.status_code}")
        except Exception as e:
            print(f"✗ Config endpoint failed: {e}")
            return False
        
        # Test status endpoint
        try:
            response = requests.get(f"http://127.0.0.1:{test_port}/api/status", timeout=5)
            if response.status_code == 200:
                print("✓ Status endpoint responded successfully")
            else:
                print(f"✗ Status endpoint returned status {response.status_code}")
        except Exception as e:
            print(f"✗ Status endpoint failed: {e}")
            return False
        
        # Test graceful shutdown
        print("Testing graceful shutdown...")
        app.shutdown()
        
        # Wait for shutdown to complete
        time.sleep(5)
        
        # Verify server is no longer responding
        server_stopped = False
        for attempt in range(5):
            try:
                response = requests.get(f"http://127.0.0.1:{test_port}/api/health", timeout=1)
                if attempt < 4:
                    time.sleep(1)
                    continue
                else:
                    print("✗ Server still responding after shutdown")
                    return False
            except requests.exceptions.RequestException:
                server_stopped = True
                break
        
        if server_stopped:
            print("✓ Server stopped responding after shutdown")
        else:
            print("✗ Server failed to stop within timeout")
        
        print("\n" + "="*60)
        print("  COMPLETE STARTUP TEST PASSED")
        print("="*60)
        return True
        
    except Exception as e:
        print(f"✗ Complete startup test failed: {e}")
        return False

def test_command_line_interface():
    """Test command-line interface functionality"""
    print("\nTesting command-line interface...")
    
    try:
        import subprocess
        
        # Test help command
        result = subprocess.run([sys.executable, 'main.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and 'Real Estate Monitor' in result.stdout:
            print("✓ Help command works correctly")
        else:
            print("✗ Help command failed")
            return False
        
        # Test version command
        result = subprocess.run([sys.executable, 'main.py', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and 'Real Estate Monitor v' in result.stdout:
            print("✓ Version command works correctly")
        else:
            print("✗ Version command failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Command-line interface test failed: {e}")
        return False

def test_startup_scripts():
    """Test startup scripts exist and are properly formatted"""
    print("\nTesting startup scripts...")
    
    try:
        # Check Windows batch script
        if os.path.exists('start.bat'):
            with open('start.bat', 'r') as f:
                content = f.read()
                if 'python main.py' in content and '@echo off' in content:
                    print("✓ Windows startup script (start.bat) exists and looks correct")
                else:
                    print("✗ Windows startup script has incorrect content")
                    return False
        else:
            print("✗ Windows startup script (start.bat) not found")
            return False
        
        # Check Unix shell script
        if os.path.exists('start.sh'):
            with open('start.sh', 'r') as f:
                content = f.read()
                if 'main.py' in content and '#!/bin/bash' in content:
                    print("✓ Unix startup script (start.sh) exists and looks correct")
                else:
                    print("✗ Unix startup script has incorrect content")
                    return False
        else:
            print("✗ Unix startup script (start.sh) not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Startup scripts test failed: {e}")
        return False

def test_deployment_documentation():
    """Test that deployment documentation exists"""
    print("\nTesting deployment documentation...")
    
    try:
        if os.path.exists('DEPLOYMENT.md'):
            with open('DEPLOYMENT.md', 'r') as f:
                content = f.read()
                required_sections = [
                    'System Requirements',
                    'Quick Start',
                    'Command Line Options',
                    'Configuration',
                    'Troubleshooting'
                ]
                
                missing_sections = []
                for section in required_sections:
                    if section not in content:
                        missing_sections.append(section)
                
                if missing_sections:
                    print(f"✗ Deployment documentation missing sections: {missing_sections}")
                    return False
                else:
                    print("✓ Deployment documentation exists with all required sections")
                    return True
        else:
            print("✗ Deployment documentation (DEPLOYMENT.md) not found")
            return False
        
    except Exception as e:
        print(f"✗ Deployment documentation test failed: {e}")
        return False

def run_all_tests():
    """Run all complete startup tests"""
    tests = [
        test_command_line_interface,
        test_startup_scripts,
        test_deployment_documentation,
        test_complete_startup_shutdown
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "="*60)
    print(f"  FINAL RESULTS: {passed} passed, {failed} failed")
    print("="*60)
    
    if failed == 0:
        print("\n✓ All tests passed! The application is ready for deployment.")
        return True
    else:
        print(f"\n✗ {failed} tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)