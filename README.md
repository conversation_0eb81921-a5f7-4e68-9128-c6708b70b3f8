# Real Estate Monitor

A web-based application that monitors meadownorth.ca for specific property addresses and provides real-time notifications when target properties appear or disappear from listings.

## Project Structure

```
real-estate-monitor/
├── backend/                 # Backend Python modules
│   └── __init__.py
├── frontend/               # Frontend web interface
│   └── index.html
├── shared/                 # Shared utilities and models
│   ├── __init__.py
│   ├── models.py          # Core data models (PropertyListing, Config)
│   └── logging_config.py  # Logging configuration
├── logs/                  # Application logs (created automatically)
├── requirements.txt       # Python dependencies
├── test_models.py        # Basic model tests
├── test_logging.py       # Logging tests
└── README.md             # This file
```

## Core Data Models

### PropertyListing
Represents a property listing with validation for required fields:
- `address`: Property address (required)
- `price`: Listing price (required)  
- `mls_number`: MLS number (required)
- `description`: Property description (required)
- Optional fields: `square_footage`, `bedrooms`, `bathrooms`, `lot_size`, `year_built`, `image_url`, `listing_url`
- `first_seen`: Timestamp when listing was first detected

### Config
Application configuration with validation:
- `target_address`: Address to monitor (required, validated format)
- `check_interval_minutes`: How often to check (default: 15 minutes)
- `notification_enabled`: Whether notifications are enabled (default: True)
- `last_check`: Timestamp of last check
- `found_listings`: List of found MLS numbers

## Installation

```bash
pip install -r requirements.txt
```

## Testing

Run basic model tests:
```bash
python test_models.py
```

Run logging tests:
```bash
python test_logging.py
```

## Next Steps

This completes the basic project structure and core data models. The next tasks will implement:
1. Web scraper for meadownorth.ca
2. Address matching system
3. Configuration management
4. Flask backend API
5. Frontend interface
6. Monitoring service
7. Notification system