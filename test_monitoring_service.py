"""
Integration tests for the MonitoringService class.
Tests the complete monitoring lifecycle including threading and event handling.
"""
import pytest
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from typing import List

from backend.monitoring_service import MonitoringService, MonitoringEvent
from shared.config_manager import ConfigManager
from shared.models import Config, PropertyListing
from shared.address_matcher import AddressMatcher
from backend.scraper import MeadowNorthScraper


class TestMonitoringService:
    """Test suite for MonitoringService"""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create a mock ConfigManager"""
        mock_cm = Mock(spec=ConfigManager)
        mock_config = Config(
            target_address="123 Test Street",
            check_interval_minutes=1,  # Short interval for testing
            notification_enabled=True,
            found_listings=[]
        )
        mock_cm.load_config.return_value = mock_config
        mock_cm.needs_address_setup.return_value = False
        return mock_cm
    
    @pytest.fixture
    def mock_scraper(self):
        """Create a mock MeadowNorthScraper"""
        mock_scraper = Mock(spec=MeadowNorthScraper)
        # Default to returning empty listings
        mock_scraper.fetch_listings.return_value = []
        return mock_scraper
    
    @pytest.fixture
    def mock_address_matcher(self):
        """Create a mock AddressMatcher"""
        mock_matcher = Mock(spec=AddressMatcher)
        # Default to no matches
        mock_matcher.find_matches.return_value = []
        return mock_matcher
    
    @pytest.fixture
    def temp_data_dir(self, tmp_path):
        """Create a temporary data directory for testing"""
        return str(tmp_path / "test_data")
    
    @pytest.fixture
    def monitoring_service(self, mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir):
        """Create a MonitoringService instance with mocked dependencies"""
        service = MonitoringService(mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir)
        yield service
        # Cleanup: ensure service is stopped after each test
        if service.is_running():
            service.stop_monitoring()
    
    def test_initial_state(self, monitoring_service):
        """Test initial state of monitoring service"""
        assert not monitoring_service.is_running()
        assert monitoring_service.get_status() == "stopped"
        assert monitoring_service.get_last_check_time() is None
        assert len(monitoring_service.get_current_listings()) == 0
        assert len(monitoring_service.get_found_properties()) == 0
    
    def test_start_monitoring_success(self, monitoring_service):
        """Test successful start of monitoring service"""
        # Start monitoring
        result = monitoring_service.start_monitoring()
        
        assert result is True
        assert monitoring_service.is_running()
        assert monitoring_service.get_status() in ["starting", "active"]
        
        # Give the thread a moment to start
        time.sleep(0.1)
        assert monitoring_service.get_status() == "active"
    
    def test_start_monitoring_already_running(self, monitoring_service):
        """Test starting monitoring when already running"""
        # Start monitoring first time
        monitoring_service.start_monitoring()
        
        # Try to start again
        result = monitoring_service.start_monitoring()
        
        assert result is False
        assert monitoring_service.is_running()
    
    def test_start_monitoring_invalid_config(self, monitoring_service, mock_config_manager):
        """Test starting monitoring with invalid configuration"""
        # Mock needs_address_setup to return True
        mock_config_manager.needs_address_setup.return_value = True
        
        result = monitoring_service.start_monitoring()
        
        assert result is False
        assert not monitoring_service.is_running()
        assert monitoring_service.get_status() == "error"
    
    def test_stop_monitoring_success(self, monitoring_service):
        """Test successful stop of monitoring service"""
        # Start monitoring first
        monitoring_service.start_monitoring()
        time.sleep(0.1)  # Let it start
        
        # Stop monitoring
        result = monitoring_service.stop_monitoring()
        
        assert result is True
        assert not monitoring_service.is_running()
        assert monitoring_service.get_status() == "stopped"
    
    def test_stop_monitoring_not_running(self, monitoring_service):
        """Test stopping monitoring when not running"""
        result = monitoring_service.stop_monitoring()
        
        assert result is True  # Should succeed even if not running
        assert not monitoring_service.is_running()
    
    def test_restart_monitoring(self, monitoring_service):
        """Test restarting monitoring service"""
        # Start monitoring
        monitoring_service.start_monitoring()
        time.sleep(0.1)
        assert monitoring_service.is_running()
        
        # Restart monitoring
        result = monitoring_service.restart_monitoring()
        
        assert result is True
        assert monitoring_service.is_running()
        assert monitoring_service.get_status() == "active"
    
    def test_immediate_check(self, monitoring_service, mock_scraper):
        """Test performing immediate check"""
        # Setup mock scraper to return test listings
        test_listings = [
            PropertyListing(
                address="123 Test Street",
                price="$500,000",
                mls_number="TEST123",
                description="Test property"
            )
        ]
        mock_scraper.fetch_listings.return_value = test_listings
        
        # Perform immediate check
        result = monitoring_service.perform_immediate_check()
        
        assert len(result) == 1
        assert result[0].address == "123 Test Street"
        assert monitoring_service.get_last_check_time() is not None
        assert len(monitoring_service.get_current_listings()) == 1
    
    def test_immediate_check_with_error(self, monitoring_service, mock_scraper):
        """Test immediate check with scraper error"""
        # Setup mock scraper to raise exception
        mock_scraper.fetch_listings.side_effect = Exception("Scraper error")
        
        # Perform immediate check should raise exception
        with pytest.raises(Exception, match="Scraper error"):
            monitoring_service.perform_immediate_check()
        
        assert monitoring_service.get_status() == "error"
    
    def test_event_callbacks(self, monitoring_service):
        """Test event callback system"""
        events_received = []
        
        def event_handler(event: MonitoringEvent):
            events_received.append(event)
        
        # Add callback
        monitoring_service.add_event_callback(event_handler)
        
        # Start monitoring (should trigger status_change event)
        monitoring_service.start_monitoring()
        time.sleep(0.1)
        
        # Stop monitoring (should trigger another status_change event)
        monitoring_service.stop_monitoring()
        
        # Check that events were received
        assert len(events_received) >= 2
        assert any(event.event_type == "status_change" and "started" in event.message 
                  for event in events_received)
        assert any(event.event_type == "status_change" and "stopped" in event.message 
                  for event in events_received)
    
    def test_remove_event_callback(self, monitoring_service):
        """Test removing event callbacks"""
        events_received = []
        
        def event_handler(event: MonitoringEvent):
            events_received.append(event)
        
        # Add and then remove callback
        monitoring_service.add_event_callback(event_handler)
        monitoring_service.remove_event_callback(event_handler)
        
        # Start monitoring
        monitoring_service.start_monitoring()
        time.sleep(0.1)
        monitoring_service.stop_monitoring()
        
        # Should not receive any events
        assert len(events_received) == 0
    
    def test_property_found_event(self, monitoring_service, mock_scraper, mock_address_matcher):
        """Test property found event generation"""
        events_received = []
        
        def event_handler(event: MonitoringEvent):
            events_received.append(event)
        
        monitoring_service.add_event_callback(event_handler)
        
        # Setup mocks to simulate finding a property
        test_listing = PropertyListing(
            address="123 Test Street",
            price="$500,000",
            mls_number="TEST123",
            description="Test property"
        )
        mock_scraper.fetch_listings.return_value = [test_listing]
        mock_address_matcher.find_matches.return_value = [(test_listing, 0.95)]
        
        # Perform check
        monitoring_service.perform_immediate_check()
        
        # Check for property found event
        property_events = [e for e in events_received if e.event_type == "property_found"]
        assert len(property_events) == 1
        assert property_events[0].property_listing == test_listing
        assert "TEST123" in property_events[0].message
    
    def test_property_removed_event(self, monitoring_service, mock_scraper, mock_address_matcher, mock_config_manager):
        """Test property removed event generation"""
        events_received = []
        
        def event_handler(event: MonitoringEvent):
            events_received.append(event)
        
        monitoring_service.add_event_callback(event_handler)
        
        # Setup initial state with found property
        monitoring_service.found_properties.add("TEST123")
        mock_config = mock_config_manager.load_config.return_value
        mock_config.found_listings = ["TEST123"]
        
        # Setup mocks to simulate property no longer found
        mock_scraper.fetch_listings.return_value = []
        mock_address_matcher.find_matches.return_value = []
        
        # Perform check
        monitoring_service.perform_immediate_check()
        
        # Check for property removed event
        property_events = [e for e in events_received if e.event_type == "property_removed"]
        assert len(property_events) == 1
        assert "TEST123" in property_events[0].message
    
    def test_monitoring_loop_with_interval(self, monitoring_service, mock_scraper, mock_config_manager):
        """Test monitoring loop respects check interval"""
        # Set very short interval for testing
        mock_config = mock_config_manager.load_config.return_value
        mock_config.check_interval_minutes = 0.01  # 0.6 seconds
        
        check_count = 0
        original_fetch = mock_scraper.fetch_listings
        
        def count_fetches():
            nonlocal check_count
            check_count += 1
            return []
        
        mock_scraper.fetch_listings.side_effect = count_fetches
        
        # Start monitoring
        monitoring_service.start_monitoring()
        
        # Wait for multiple checks
        time.sleep(2.0)
        
        # Stop monitoring
        monitoring_service.stop_monitoring()
        
        # Should have performed multiple checks
        assert check_count >= 2
    
    def test_monitoring_loop_error_recovery(self, monitoring_service, mock_scraper):
        """Test monitoring loop handles errors and continues"""
        call_count = 0
        
        def failing_fetch():
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("Temporary error")
            return []
        
        mock_scraper.fetch_listings.side_effect = failing_fetch
        
        # Start monitoring
        monitoring_service.start_monitoring()
        
        # Wait for error recovery
        time.sleep(2.0)
        
        # Stop monitoring
        monitoring_service.stop_monitoring()
        
        # Should have attempted multiple calls despite errors
        assert call_count >= 3
    
    def test_graceful_shutdown_with_context_manager(self, mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir):
        """Test graceful shutdown using context manager"""
        with MonitoringService(mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir) as service:
            service.start_monitoring()
            time.sleep(0.1)
            assert service.is_running()
        
        # Service should be stopped after exiting context
        assert not service.is_running()
    
    def test_thread_safety(self, monitoring_service, mock_scraper):
        """Test thread safety of monitoring service operations"""
        # Setup mock to return different results
        mock_scraper.fetch_listings.return_value = []
        
        # Start monitoring
        monitoring_service.start_monitoring()
        time.sleep(0.1)
        
        # Perform multiple immediate checks from different threads
        results = []
        errors = []
        
        def perform_check():
            try:
                result = monitoring_service.perform_immediate_check()
                results.append(len(result))
            except Exception as e:
                errors.append(e)
        
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=perform_check)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Stop monitoring
        monitoring_service.stop_monitoring()
        
        # All checks should have completed successfully
        assert len(errors) == 0
        assert len(results) == 5
    
    def test_load_initial_state(self, mock_config_manager, mock_scraper, mock_address_matcher):
        """Test loading initial state from configuration"""
        # Setup config with existing found listings
        mock_config = Config(
            target_address="123 Test Street",
            check_interval_minutes=15,
            found_listings=["MLS001", "MLS002"],
            last_check=datetime.now() - timedelta(hours=1)
        )
        mock_config_manager.load_config.return_value = mock_config
        
        # Create service (should load initial state)
        service = MonitoringService(mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir)
        
        # Check initial state was loaded
        assert len(service.get_found_properties()) == 2
        assert "MLS001" in service.get_found_properties()
        assert "MLS002" in service.get_found_properties()
        assert service.get_last_check_time() is not None
    
    def test_state_persistence_on_start_stop(self, monitoring_service):
        """Test that monitoring state is persisted on start and stop"""
        # Start monitoring
        assert monitoring_service.start_monitoring()
        time.sleep(0.1)  # Let it run briefly
        
        # Stop monitoring
        assert monitoring_service.stop_monitoring()
        
        # Check that state was saved
        saved_state = monitoring_service.persistence_manager.load_monitoring_state()
        assert saved_state["current_status"] == "stopped"
        assert saved_state["is_running"] == False
    
    def test_listings_history_persistence(self, monitoring_service, mock_scraper):
        """Test that listings are saved to history during monitoring"""
        # Create sample listings
        sample_listings = [
            PropertyListing(
                address="123 Sample Street",
                price="$500,000",
                mls_number="SAMPLE123",
                description="Sample property"
            )
        ]
        
        mock_scraper.fetch_listings.return_value = sample_listings
        
        # Start monitoring and let it run one cycle
        assert monitoring_service.start_monitoring()
        time.sleep(0.1)
        assert monitoring_service.stop_monitoring()
        
        # Check that listings were saved to history
        history = monitoring_service.get_listings_history()
        assert len(history) == 1
        assert history[0].mls_number == "SAMPLE123"
    
    def test_state_restoration_on_init(self, mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir):
        """Test that monitoring state is restored on initialization"""
        # Create initial service and save some state
        service1 = MonitoringService(mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir)
        
        # Manually set some state and save it
        service1.last_check_time = datetime.now()
        service1._save_monitoring_state()
        
        # Create new service instance (should restore state)
        service2 = MonitoringService(mock_config_manager, mock_scraper, mock_address_matcher, temp_data_dir)
        
        # Check that state was restored
        assert service2.last_check_time is not None
        assert abs((service2.last_check_time - service1.last_check_time).total_seconds()) < 1
    
    def test_data_persistence_error_handling(self, monitoring_service):
        """Test that monitoring continues even if data persistence fails"""
        # Mock persistence manager to raise errors
        monitoring_service.persistence_manager.save_monitoring_state = Mock(side_effect=Exception("Persistence error"))
        monitoring_service.persistence_manager.save_listings_history = Mock(side_effect=Exception("History error"))
        
        # Monitoring should still work despite persistence errors
        assert monitoring_service.start_monitoring()
        time.sleep(0.1)
        assert monitoring_service.stop_monitoring()
        
        # Service should still function normally
        assert monitoring_service.get_status() == "stopped"


class TestMonitoringEvent:
    """Test suite for MonitoringEvent dataclass"""
    
    def test_monitoring_event_creation(self):
        """Test creating MonitoringEvent instances"""
        timestamp = datetime.now()
        
        # Test basic event
        event = MonitoringEvent(
            event_type="status_change",
            timestamp=timestamp,
            message="Test message"
        )
        
        assert event.event_type == "status_change"
        assert event.timestamp == timestamp
        assert event.message == "Test message"
        assert event.property_listing is None
        assert event.error is None
    
    def test_monitoring_event_with_property(self):
        """Test MonitoringEvent with property listing"""
        listing = PropertyListing(
            address="123 Test St",
            price="$500,000",
            mls_number="TEST123",
            description="Test property"
        )
        
        event = MonitoringEvent(
            event_type="property_found",
            timestamp=datetime.now(),
            message="Property found",
            property_listing=listing
        )
        
        assert event.property_listing == listing
        assert event.property_listing.mls_number == "TEST123"
    
    def test_monitoring_event_with_error(self):
        """Test MonitoringEvent with error"""
        error = Exception("Test error")
        
        event = MonitoringEvent(
            event_type="error",
            timestamp=datetime.now(),
            message="Error occurred",
            error=error
        )
        
        assert event.error == error
        assert str(event.error) == "Test error"





if __name__ == "__main__":
    pytest.main([__file__, "-v"])