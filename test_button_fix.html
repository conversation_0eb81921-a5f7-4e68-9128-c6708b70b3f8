<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { margin: 10px; padding: 10px 20px; }
    </style>
</head>
<body>
    <h1>Real Estate Monitor - Button Fix Test</h1>
    
    <div id="test-results"></div>
    
    <button onclick="testRealEstateMonitor()">Test RealEstateMonitor Class</button>
    <button onclick="testButtonElements()">Test Button Elements</button>
    
    <script src="frontend/app.js"></script>
    <script>
        function addTestResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function testRealEstateMonitor() {
            try {
                // Test if we can create the class without errors
                const monitor = new RealEstateMonitor();
                addTestResult('✓ RealEstateMonitor class created successfully');
                
                // Test if logger works
                monitor.logger.info('Test log message');
                addTestResult('✓ Logger is working');
                
                // Test if methods exist
                if (typeof monitor.openConfigPanel === 'function') {
                    addTestResult('✓ openConfigPanel method exists');
                } else {
                    addTestResult('✗ openConfigPanel method missing', false);
                }
                
            } catch (error) {
                addTestResult(`✗ Error creating RealEstateMonitor: ${error.message}`, false);
            }
        }
        
        function testButtonElements() {
            // Create mock HTML elements for testing
            const mockHTML = `
                <button id="config-btn">Settings</button>
                <div id="config-overlay" class="config-overlay">
                    <div class="config-panel">
                        <button id="config-close">Close</button>
                        <button id="config-cancel">Cancel</button>
                    </div>
                </div>
            `;
            
            // Add mock elements to page
            const testContainer = document.createElement('div');
            testContainer.innerHTML = mockHTML;
            document.body.appendChild(testContainer);
            
            try {
                const monitor = new RealEstateMonitor();
                
                // Test button click
                const configBtn = document.getElementById('config-btn');
                if (configBtn) {
                    configBtn.click();
                    addTestResult('✓ Config button click executed without error');
                } else {
                    addTestResult('✗ Config button not found', false);
                }
                
            } catch (error) {
                addTestResult(`✗ Button test error: ${error.message}`, false);
            }
        }
        
        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            addTestResult('Page loaded, running basic test...');
            testRealEstateMonitor();
        });
    </script>
</body>
</html>