#!/usr/bin/env python3
"""
Test the updated address matching to ensure it's more precise
"""
from shared.address_matcher import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_address_matching():
    """Test the address matching with the fix"""
    matcher = AddressMatcher(similarity_threshold=0.8)
    
    target_address = "810 Centre STREET"
    
    test_addresses = [
        "810 Centre STREET",    # Should match (1.0)
        "614 Centre STREET",    # Should NOT match (< 0.8)
        "810 Center STREET",    # Should match (typo tolerance)
        "810 Centre St",        # Should match (abbreviation)
        "810 Centre Street",    # Should match (case difference)
    ]
    
    print(f"🎯 Testing address matching for target: {target_address}")
    print(f"📏 Similarity threshold: {matcher.similarity_threshold}")
    print("=" * 60)
    
    for test_addr in test_addresses:
        similarity = matcher.calculate_similarity(target_address, test_addr)
        is_match = matcher.is_match(target_address, test_addr)
        
        print(f"Address: '{test_addr}'")
        print(f"  Similarity: {similarity:.3f}")
        print(f"  Match: {'✅ YES' if is_match else '❌ NO'}")
        print()

if __name__ == "__main__":
    test_address_matching()