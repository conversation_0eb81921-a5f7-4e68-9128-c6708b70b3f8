#!/usr/bin/env python3
"""
Test the API fix to ensure only matching listings are returned
"""
import requests
import json

def test_api_fix():
    """Test that the API returns only matching listings"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 Testing API fix for listings endpoint")
    print("=" * 50)
    
    try:
        # Test the listings endpoint
        print("📡 Testing /api/listings endpoint...")
        response = requests.get(f"{base_url}/api/listings")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                listings = data['data']['listings']
                count = data['data']['count']
                
                print(f"✅ API returned {count} listings")
                
                for i, listing in enumerate(listings, 1):
                    print(f"{i:2d}. {listing['address']} - {listing['price']} (MLS: {listing['mls_number']})")
                
                if count == 2:
                    print("✅ SUCCESS: API now returns only matching listings!")
                elif count == 14:
                    print("❌ FAILURE: API still returns all listings")
                else:
                    print(f"⚠️  UNEXPECTED: API returned {count} listings")
            else:
                print(f"❌ API error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            
        # Test the status endpoint
        print("\n📊 Testing /api/status endpoint...")
        response = requests.get(f"{base_url}/api/status")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                status_data = data['data']
                total_count = status_data.get('total_listings_count', 0)
                matching_count = status_data.get('matching_listings_count', 0)
                
                print(f"✅ Status shows {matching_count} matching out of {total_count} total listings")
            else:
                print(f"❌ Status API error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ Status HTTP error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API server. Make sure it's running on port 5000.")
    except Exception as e:
        print(f"❌ Error testing API: {e}")

if __name__ == "__main__":
    test_api_fix()