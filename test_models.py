"""
Basic tests for the core data models to verify they work correctly.
"""
from datetime import datetime
import pytest
from shared.models import PropertyListing, Config


def test_property_listing_creation():
    """Test creating a valid PropertyListing"""
    listing = PropertyListing(
        address="123 Main Street",
        price="$500,000",
        mls_number="ML123456",
        description="Beautiful home with great features"
    )
    
    assert listing.address == "123 Main Street"
    assert listing.price == "$500,000"
    assert listing.mls_number == "ML123456"
    assert listing.description == "Beautiful home with great features"
    assert isinstance(listing.first_seen, datetime)


def test_property_listing_validation():
    """Test PropertyListing validation"""
    # Test empty address
    with pytest.raises(ValueError, match="Address cannot be empty"):
        PropertyListing(address="", price="$500,000", mls_number="ML123456", description="Test")
    
    # Test empty price
    with pytest.raises(ValueError, match="Price cannot be empty"):
        PropertyListing(address="123 Main St", price="", mls_number="ML123456", description="Test")
    
    # Test empty MLS number
    with pytest.raises(ValueError, match="MLS number cannot be empty"):
        PropertyListing(address="123 Main St", price="$500,000", mls_number="", description="Test")
    
    # Test negative bedrooms
    with pytest.raises(ValueError, match="Bedrooms must be non-negative"):
        PropertyListing(
            address="123 Main St", 
            price="$500,000", 
            mls_number="ML123456", 
            description="Test",
            bedrooms=-1
        )


def test_config_creation():
    """Test creating a valid Config"""
    config = Config(target_address="456 Oak Avenue")
    
    assert config.target_address == "456 Oak Avenue"
    assert config.check_interval_minutes == 15  # default value
    assert config.notification_enabled is True  # default value
    assert config.last_check is None  # default value
    assert config.found_listings == []  # default value


def test_config_validation():
    """Test Config validation"""
    # Test empty target address
    with pytest.raises(ValueError, match="Target address cannot be empty"):
        Config(target_address="")
    
    # Test invalid check interval
    with pytest.raises(ValueError, match="Check interval must be positive"):
        Config(target_address="123 Main St", check_interval_minutes=0)
    
    with pytest.raises(ValueError, match="Check interval must be at least 1 minute"):
        Config(target_address="123 Main St", check_interval_minutes=0.5)
    
    # Test invalid address format
    with pytest.raises(ValueError, match="Target address format appears invalid"):
        Config(target_address="invalid address format")


def test_config_serialization():
    """Test Config to_dict and from_dict methods"""
    original_config = Config(
        target_address="789 Pine Street",
        check_interval_minutes=30,
        notification_enabled=False,
        last_check=datetime(2024, 1, 15, 10, 30, 0),
        found_listings=["ML123456", "ML789012"]
    )
    
    # Convert to dict
    config_dict = original_config.to_dict()
    
    # Convert back to Config
    restored_config = Config.from_dict(config_dict)
    
    assert restored_config.target_address == original_config.target_address
    assert restored_config.check_interval_minutes == original_config.check_interval_minutes
    assert restored_config.notification_enabled == original_config.notification_enabled
    assert restored_config.last_check == original_config.last_check
    assert restored_config.found_listings == original_config.found_listings


if __name__ == "__main__":
    # Run basic tests manually
    print("Testing PropertyListing creation...")
    test_property_listing_creation()
    print("✓ PropertyListing creation test passed")
    
    print("Testing Config creation...")
    test_config_creation()
    print("✓ Config creation test passed")
    
    print("Testing Config serialization...")
    test_config_serialization()
    print("✓ Config serialization test passed")
    
    print("All basic tests passed!")