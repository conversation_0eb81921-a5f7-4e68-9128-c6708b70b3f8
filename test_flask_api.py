"""
Tests for the Flask backend API server.
"""
import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from backend.app import RealEstateMonitorAPI
from shared.models import PropertyListing, Config


@pytest.fixture
def api_client():
    """Create a test client for the Flask API"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Use temporary directory for test config
        with patch('shared.config_manager.ConfigManager.DEFAULT_DATA_DIR', temp_dir):
            api = RealEstateMonitorAPI()
            api.app.config['TESTING'] = True
            
            # Create a test config
            test_config = Config(
                target_address="123 Test Street",
                check_interval_minutes=15,
                notification_enabled=True
            )
            api.config_manager.save_config(test_config)
            
            with api.app.test_client() as client:
                yield client, api


class TestFlaskAPI:
    """Test cases for the Flask API endpoints"""
    
    def test_get_config(self, api_client):
        """Test GET /api/config endpoint"""
        client, api = api_client
        
        response = client.get('/api/config')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert data['data']['target_address'] == "123 Test Street"
        assert data['data']['check_interval_minutes'] == 15
        assert data['data']['notification_enabled'] is True
    
    def test_update_config_valid(self, api_client):
        """Test POST /api/config with valid data"""
        client, api = api_client
        
        update_data = {
            'target_address': '456 New Street',
            'check_interval_minutes': 30,
            'notification_enabled': False
        }
        
        response = client.post('/api/config', 
                             data=json.dumps(update_data),
                             content_type='application/json')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'message' in data
        
        # Verify config was updated
        config_response = client.get('/api/config')
        config_data = json.loads(config_response.data)
        assert config_data['data']['target_address'] == '456 New Street'
        assert config_data['data']['check_interval_minutes'] == 30
        assert config_data['data']['notification_enabled'] is False
    
    def test_update_config_invalid_address(self, api_client):
        """Test POST /api/config with invalid address"""
        client, api = api_client
        
        update_data = {
            'target_address': ''  # Empty address
        }
        
        response = client.post('/api/config',
                             data=json.dumps(update_data),
                             content_type='application/json')
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
    
    def test_update_config_invalid_interval(self, api_client):
        """Test POST /api/config with invalid check interval"""
        client, api = api_client
        
        update_data = {
            'check_interval_minutes': -5  # Negative interval
        }
        
        response = client.post('/api/config',
                             data=json.dumps(update_data),
                             content_type='application/json')
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
    
    def test_get_status(self, api_client):
        """Test GET /api/status endpoint"""
        client, api = api_client
        
        response = client.get('/api/status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'monitoring_active' in data['data']
        assert 'monitoring_status' in data['data']
        assert 'target_address' in data['data']
        assert data['data']['monitoring_active'] is False
        assert data['data']['monitoring_status'] == "stopped"
    
    def test_get_listings_empty(self, api_client):
        """Test GET /api/listings endpoint with no listings"""
        client, api = api_client
        
        response = client.get('/api/listings')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert data['data']['listings'] == []
        assert data['data']['count'] == 0
    
    def test_get_listings_with_data(self, api_client):
        """Test GET /api/listings endpoint with mock listings"""
        client, api = api_client
        
        # Add mock listings to the monitoring service
        mock_listing = PropertyListing(
            address="123 Test Street",
            price="$500,000",
            mls_number="TEST123",
            description="Test property",
            first_seen=datetime.now()
        )
        api.monitoring_service.current_listings = [mock_listing]
        
        response = client.get('/api/listings')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['count'] == 1
        assert len(data['data']['listings']) == 1
        assert data['data']['listings'][0]['address'] == "123 Test Street"
        assert data['data']['listings'][0]['price'] == "$500,000"
        assert data['data']['listings'][0]['mls_number'] == "TEST123"
    
    def test_start_monitoring_success(self, api_client):
        """Test POST /api/start-monitoring endpoint"""
        client, api = api_client
        
        with patch.object(api.monitoring_service, 'start_monitoring', return_value=True) as mock_start:
            response = client.post('/api/start-monitoring')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert 'message' in data
            
            # Verify monitoring service start was called
            mock_start.assert_called_once()
    
    def test_start_monitoring_already_active(self, api_client):
        """Test POST /api/start-monitoring when already active"""
        client, api = api_client
        
        # Mock monitoring service as already running
        with patch.object(api.monitoring_service, 'is_running', return_value=True):
            response = client.post('/api/start-monitoring')
            assert response.status_code == 400
            
            data = json.loads(response.data)
            assert data['success'] is False
            assert 'error' in data
    
    def test_stop_monitoring_success(self, api_client):
        """Test POST /api/stop-monitoring endpoint"""
        client, api = api_client
        
        # Mock monitoring service as running and successful stop
        with patch.object(api.monitoring_service, 'is_running', return_value=True), \
             patch.object(api.monitoring_service, 'stop_monitoring', return_value=True) as mock_stop:
            
            response = client.post('/api/stop-monitoring')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert 'message' in data
            
            # Verify monitoring service stop was called
            mock_stop.assert_called_once()
    
    def test_stop_monitoring_not_active(self, api_client):
        """Test POST /api/stop-monitoring when not active"""
        client, api = api_client
        
        # Mock monitoring service as not running
        with patch.object(api.monitoring_service, 'is_running', return_value=False):
            response = client.post('/api/stop-monitoring')
            assert response.status_code == 400
            
            data = json.loads(response.data)
            assert data['success'] is False
            assert 'error' in data
    
    def test_check_now_success(self, api_client):
        """Test POST /api/check-now endpoint"""
        client, api = api_client
        
        # Mock the monitoring service immediate check
        mock_listing = PropertyListing(
            address="123 Test Street",
            price="$500,000",
            mls_number="TEST123",
            description="Test property"
        )
        
        with patch.object(api.monitoring_service, 'perform_immediate_check', return_value=[mock_listing]) as mock_check, \
             patch.object(api.monitoring_service, 'get_last_check_time', return_value=datetime.now()) as mock_time:
            
            response = client.post('/api/check-now')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['success'] is True
            assert 'message' in data
            assert 'data' in data
            assert data['data']['listings_count'] == 1
            
            # Verify monitoring service check was called
            mock_check.assert_called_once()
    
    def test_error_handlers(self, api_client):
        """Test error handlers"""
        client, api = api_client
        
        # Test 404 handler
        response = client.get('/api/nonexistent')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
        
        # Test 405 handler (method not allowed)
        response = client.put('/api/status')  # PUT not allowed on status endpoint
        assert response.status_code == 405
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
    
    def test_cors_headers(self, api_client):
        """Test that CORS headers are present"""
        client, api = api_client
        
        response = client.get('/api/status')
        assert response.status_code == 200
        
        # CORS headers should be present (Flask-CORS adds them automatically)
        # The exact headers depend on Flask-CORS configuration
        # We just verify the response is successful, indicating CORS is working


if __name__ == '__main__':
    pytest.main([__file__])