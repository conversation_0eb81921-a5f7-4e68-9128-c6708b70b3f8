#!/usr/bin/env python3
"""
Debug script to test address matching for "810 Centre STREET" on meadownorth.ca
"""
import sys
import logging
from datetime import datetime
from backend.scraper import MeadowNorthScraper
from shared.address_matcher import AddressMatcher
from shared.models import PropertyListing

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_address_matching():
    """Debug the address matching issue"""
    target_address = "810 Centre STREET"
    
    print(f"🔍 Debugging address matching for: {target_address}")
    print("=" * 60)
    
    # Initialize components
    scraper = MeadowNorthScraper()
    matcher = AddressMatcher(similarity_threshold=0.8)
    
    try:
        # Fetch listings from the website
        print("📡 Fetching listings from meadownorth.ca...")
        listings = scraper.fetch_listings()
        print(f"✅ Found {len(listings)} total listings")
        
        if not listings:
            print("❌ No listings found - this might be the issue!")
            return
        
        print("\n📋 All found listings:")
        print("-" * 40)
        for i, listing in enumerate(listings, 1):
            print(f"{i:2d}. {listing.address} - {listing.price}")
        
        # Test address normalization
        print(f"\n🔧 Address normalization:")
        normalized_target = matcher.normalize_address(target_address)
        print(f"Target: '{target_address}' → '{normalized_target}'")
        
        # Check each listing for similarity
        print(f"\n🎯 Similarity analysis:")
        print("-" * 40)
        similarities = []
        
        for listing in listings:
            similarity = matcher.calculate_similarity(target_address, listing.address)
            similarities.append((listing, similarity))
            
            # Get detailed explanation
            explanation = matcher.get_similarity_explanation(target_address, listing.address)
            
            print(f"Listing: '{listing.address}'")
            print(f"  Normalized: '{explanation['normalized_listing']}'")
            print(f"  Similarity: {similarity:.3f} {'✅' if similarity >= 0.8 else '❌'}")
            
            # Show potential matches (similarity > 0.5)
            if similarity > 0.5:
                print(f"  🔍 POTENTIAL MATCH (score: {similarity:.3f})")
            print()
        
        # Find matches using the matcher
        matches = matcher.find_matches(target_address, listings)
        print(f"🎯 Found {len(matches)} matches with threshold {matcher.similarity_threshold}")
        
        if matches:
            print("✅ MATCHES FOUND:")
            for listing, score in matches:
                print(f"  - {listing.address} (score: {score:.3f})")
        else:
            print("❌ NO MATCHES FOUND")
            
            # Show closest matches
            similarities.sort(key=lambda x: x[1], reverse=True)
            print(f"\n🔍 Top 5 closest matches:")
            for listing, score in similarities[:5]:
                print(f"  - {listing.address} (score: {score:.3f})")
        
        # Test with different thresholds
        print(f"\n🎛️  Testing different similarity thresholds:")
        for threshold in [0.6, 0.7, 0.8, 0.9]:
            test_matcher = AddressMatcher(similarity_threshold=threshold)
            test_matches = test_matcher.find_matches(target_address, listings)
            print(f"  Threshold {threshold}: {len(test_matches)} matches")
        
        # Look for addresses containing "Centre" or "Center"
        print(f"\n🏢 Addresses containing 'Centre' or 'Center':")
        centre_addresses = []
        for listing in listings:
            if 'centre' in listing.address.lower() or 'center' in listing.address.lower():
                centre_addresses.append(listing)
                print(f"  - {listing.address}")
        
        if not centre_addresses:
            print("  None found")
        
        # Look for addresses with "810"
        print(f"\n🏠 Addresses containing '810':")
        num_810_addresses = []
        for listing in listings:
            if '810' in listing.address:
                num_810_addresses.append(listing)
                print(f"  - {listing.address}")
        
        if not num_810_addresses:
            print("  None found")
            
    except Exception as e:
        print(f"❌ Error during debugging: {str(e)}")
        logger.exception("Debug failed")

if __name__ == "__main__":
    debug_address_matching()