#!/usr/bin/env python3
"""
Real Estate Monitor - Main Application Entry Point

This script starts the Flask backend server and opens the web browser
to provide a complete application experience.
"""

import argparse
import logging
import os
import signal
import sys
import threading
import time
import webbrowser
from typing import Optional

from backend.app import RealEstateMonitorAPI
from shared.config_manager import ConfigManager
from shared.logging_config import setup_logging


class RealEstateMonitorApp:
    """Main application class that manages the complete Real Estate Monitor system"""
    
    def __init__(self, host: str = '127.0.0.1', port: int = 5000, debug: bool = False, 
                 no_browser: bool = False):
        self.host = host
        self.port = port
        self.debug = debug
        self.no_browser = no_browser
        self.api_server: Optional[RealEstateMonitorAPI] = None
        self.server_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()
        
        # Setup logging
        log_level = "DEBUG" if debug else "INFO"
        self.logger = setup_logging(log_level=log_level)
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown on Ctrl+C"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.shutdown()
            sys.exit(0)
        
        # Handle Ctrl+C (SIGINT) and termination (SIGTERM)
        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
    
    def _check_configuration(self) -> bool:
        """
        Check if the application is properly configured.
        
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            config_manager = ConfigManager()
            
            # Check if we need initial setup
            if config_manager.needs_address_setup():
                self.logger.info("First-time setup required.")
                print("\n" + "="*60)
                print("  REAL ESTATE MONITOR - FIRST TIME SETUP")
                print("="*60)
                print("\nWelcome to Real Estate Monitor!")
                print("This application will monitor meadownorth.ca for a specific address.")
                print("\nYou can configure the target address through the web interface")
                print("after the application starts.")
                print("\nStarting application...")
                return True
            
            # Load and validate existing configuration
            config = config_manager.load_config()
            if not config.target_address:
                self.logger.warning("No target address configured")
                return True  # Still allow startup, user can configure via web interface
            
            self.logger.info(f"Configuration loaded. Target address: {config.target_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"Configuration check failed: {e}")
            print(f"\nError: Failed to load configuration: {e}")
            print("Please check your configuration files and try again.")
            return False
    
    def _start_server(self):
        """Start the Flask API server in a separate thread"""
        try:
            self.logger.info(f"Starting API server on {self.host}:{self.port}")
            self.api_server = RealEstateMonitorAPI()
            
            # Run the server (this will block)
            self.api_server.run(host=self.host, port=self.port, debug=self.debug)
            
        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            self.shutdown_event.set()
    
    def _open_browser(self):
        """Open the web browser to the application URL"""
        if self.no_browser:
            self.logger.info("Browser opening disabled by --no-browser flag")
            return
        
        url = f"http://{self.host}:{self.port}"
        
        # Wait a moment for the server to start
        max_attempts = 10
        for attempt in range(max_attempts):
            try:
                import requests
                response = requests.get(f"{url}/api/health", timeout=2)
                if response.status_code in [200, 503]:  # 503 is acceptable for degraded health
                    break
            except:
                if attempt < max_attempts - 1:
                    time.sleep(1)
                    continue
                else:
                    self.logger.warning("Server health check failed, opening browser anyway")
                    break
        
        try:
            self.logger.info(f"Opening browser to {url}")
            webbrowser.open(url)
        except Exception as e:
            self.logger.warning(f"Failed to open browser: {e}")
            print(f"\nPlease manually open your browser to: {url}")
    
    def start(self):
        """Start the complete Real Estate Monitor application"""
        try:
            # Check configuration
            if not self._check_configuration():
                return False
            
            # Print startup banner
            self._print_startup_banner()
            
            # Start the API server in a separate thread
            self.server_thread = threading.Thread(target=self._start_server, daemon=True)
            self.server_thread.start()
            
            # Wait a moment for server to start, then open browser
            time.sleep(2)
            self._open_browser()
            
            # Print running status
            self._print_running_status()
            
            # Wait for shutdown signal or server thread to end
            try:
                while self.server_thread.is_alive() and not self.shutdown_event.is_set():
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Keyboard interrupt received")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start application: {e}")
            print(f"\nError: Failed to start application: {e}")
            return False
    
    def shutdown(self):
        """Gracefully shutdown the application"""
        self.logger.info("Shutting down Real Estate Monitor application...")
        
        # Set shutdown event
        self.shutdown_event.set()
        
        # Shutdown API server
        if self.api_server:
            try:
                self.api_server.shutdown()
            except Exception as e:
                self.logger.error(f"Error during API server shutdown: {e}")
        
        # Wait for server thread to finish
        if self.server_thread and self.server_thread.is_alive():
            self.logger.info("Waiting for server thread to finish...")
            self.server_thread.join(timeout=5)
        
        self.logger.info("Application shutdown complete")
        print("\nReal Estate Monitor has been shut down successfully.")
    
    def _print_startup_banner(self):
        """Print application startup banner"""
        print("\n" + "="*60)
        print("  REAL ESTATE MONITOR")
        print("="*60)
        print(f"  Server: http://{self.host}:{self.port}")
        print(f"  Debug Mode: {'ON' if self.debug else 'OFF'}")
        print(f"  Browser: {'Disabled' if self.no_browser else 'Will open automatically'}")
        print("="*60)
    
    def _print_running_status(self):
        """Print running status information"""
        print(f"\n✓ Real Estate Monitor is running!")
        print(f"  • Web Interface: http://{self.host}:{self.port}")
        print(f"  • API Health: http://{self.host}:{self.port}/api/health")
        print(f"  • Logs: Check console output above")
        print(f"\nPress Ctrl+C to stop the application")
        print("-" * 60)


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the command-line argument parser"""
    parser = argparse.ArgumentParser(
        description="Real Estate Monitor - Automated property listing monitoring for meadownorth.ca",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Start with default settings
  python main.py --debug            # Start in debug mode
  python main.py --port 8080        # Start on port 8080
  python main.py --no-browser       # Start without opening browser
  python main.py --host 0.0.0.0     # Allow external connections
        """
    )
    
    parser.add_argument(
        '--host',
        default='127.0.0.1',
        help='Host address to bind the server to (default: 127.0.0.1)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=5000,
        help='Port number to run the server on (default: 5000)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode with verbose logging'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='Do not automatically open web browser'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Real Estate Monitor v1.0.0'
    )
    
    return parser


def main():
    """Main entry point for the Real Estate Monitor application"""
    try:
        # Parse command-line arguments
        parser = create_argument_parser()
        args = parser.parse_args()
        
        # Validate port number
        if not (1 <= args.port <= 65535):
            print(f"Error: Port number must be between 1 and 65535, got {args.port}")
            sys.exit(1)
        
        # Create and start the application
        app = RealEstateMonitorApp(
            host=args.host,
            port=args.port,
            debug=args.debug,
            no_browser=args.no_browser
        )
        
        success = app.start()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\nFatal error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()