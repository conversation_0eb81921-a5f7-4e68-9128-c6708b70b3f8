#!/usr/bin/env python3
"""
Integration test for the main application entry point.
This test verifies that the main application integrates correctly with all components.
"""

import sys
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock
import threading
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_integration():
    """Test that main.py integrates correctly with all components"""
    print("="*60)
    print("  MAIN APPLICATION INTEGRATION TEST")
    print("="*60)
    
    try:
        from main import RealEstateMonitorApp
        from backend.app import RealEstateMonitorAPI
        from shared.config_manager import ConfigManager
        from shared.models import Config
        
        print("✓ All required modules imported successfully")
        
        # Test that the app can be created
        app = RealEstateMonitorApp(no_browser=True)
        print("✓ RealEstateMonitorApp created successfully")
        
        # Test configuration check
        config_result = app._check_configuration()
        assert isinstance(config_result, bool)
        print("✓ Configuration check completed")
        
        # Test that API server can be created
        api = RealEstateMonitorAPI()
        print("✓ RealEstateMonitorAPI created successfully")
        
        # Test that all required components are initialized
        assert hasattr(api, 'config_manager')
        assert hasattr(api, 'scraper')
        assert hasattr(api, 'address_matcher')
        assert hasattr(api, 'notification_manager')
        assert hasattr(api, 'monitoring_service')
        print("✓ All API components initialized")
        
        # Test graceful shutdown
        app.shutdown()
        api.shutdown()
        print("✓ Graceful shutdown completed")
        
        print("\n✓ All integration tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_requirements_coverage():
    """Test that the implementation covers all requirements from the task"""
    print("\nTesting requirements coverage...")
    
    requirements = [
        "Build main application entry point that starts both backend and opens browser",
        "Add command-line arguments for configuration and debugging", 
        "Create simple deployment instructions and requirements.txt",
        "Implement graceful shutdown handling for Ctrl+C interruption",
        "Test complete application startup and shutdown process"
    ]
    
    coverage_results = []
    
    # Check main entry point
    if os.path.exists('main.py'):
        with open('main.py', 'r') as f:
            content = f.read()
            if 'RealEstateMonitorApp' in content and 'webbrowser.open' in content:
                coverage_results.append("✓ Main entry point with browser opening")
            else:
                coverage_results.append("✗ Main entry point incomplete")
    else:
        coverage_results.append("✗ Main entry point missing")
    
    # Check command-line arguments
    if 'argparse' in content and '--debug' in content and '--port' in content:
        coverage_results.append("✓ Command-line arguments implemented")
    else:
        coverage_results.append("✗ Command-line arguments incomplete")
    
    # Check deployment instructions
    if os.path.exists('DEPLOYMENT.md') and os.path.exists('requirements.txt'):
        coverage_results.append("✓ Deployment instructions and requirements.txt exist")
    else:
        coverage_results.append("✗ Deployment documentation missing")
    
    # Check graceful shutdown
    if 'signal.signal' in content and 'shutdown' in content:
        coverage_results.append("✓ Graceful shutdown implemented")
    else:
        coverage_results.append("✗ Graceful shutdown incomplete")
    
    # Check startup scripts
    if os.path.exists('start.bat') and os.path.exists('start.sh'):
        coverage_results.append("✓ Startup scripts created")
    else:
        coverage_results.append("✗ Startup scripts missing")
    
    for result in coverage_results:
        print(f"  {result}")
    
    failed_count = sum(1 for result in coverage_results if result.startswith("✗"))
    return failed_count == 0

if __name__ == '__main__':
    print("Running main application integration tests...\n")
    
    integration_passed = test_main_integration()
    requirements_passed = test_requirements_coverage()
    
    print("\n" + "="*60)
    if integration_passed and requirements_passed:
        print("✓ ALL TESTS PASSED - Task 12 implementation is complete!")
        print("  • Main application entry point created")
        print("  • Command-line arguments implemented")
        print("  • Deployment documentation created")
        print("  • Graceful shutdown implemented")
        print("  • Startup scripts created")
        sys.exit(0)
    else:
        print("✗ Some tests failed - please review the implementation")
        sys.exit(1)