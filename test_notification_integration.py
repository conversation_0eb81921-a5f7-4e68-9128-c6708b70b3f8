"""
Integration tests for the notification system with Flask app.
"""
import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from backend.app import RealEstateMonitorAPI
from shared.models import PropertyListing
from backend.monitoring_service import MonitoringEvent


class TestNotificationIntegration:
    """Integration tests for notification system with Flask app"""
    
    def setup_method(self):
        """Set up test fixtures before each test method"""
        # Create Flask app in test mode
        self.api = RealEstateMonitorAPI()
        self.app = self.api.app
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        self.socketio_client = self.api.socketio.test_client(self.app)
        
        # Create a mock config object
        from shared.models import Config
        mock_config = Config(
            target_address="123 Test Street",
            check_interval_minutes=15,
            notification_enabled=True,
            found_listings=[]
        )
        
        # Mock the config manager to avoid file operations
        self.api.config_manager.load_config = Mock(return_value=mock_config)
        self.api.config_manager.save_config = Mock()
        self.api.config_manager.needs_address_setup = Mock(return_value=False)
        self.api.config_manager.validate_address_format = Mock(return_value=True)
        
        # Create test property listing
        self.test_listing = PropertyListing(
            address="123 Integration Test Street",
            price="$750,000",
            mls_number="INT123",
            description="Integration test property listing.",
            bedrooms=4,
            bathrooms=3,
            square_footage="2,000 sq ft"
        )
    
    def test_notification_settings_endpoints(self):
        """Test notification settings API endpoints"""
        # Test GET notification settings
        response = self.client.get('/api/notifications/settings')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        
        settings = data['data']
        assert 'desktop_notifications_enabled' in settings
        assert 'desktop_notifications_available' in settings
        assert 'web_notifications_enabled' in settings
        assert 'app_name' in settings
        
        # Test POST notification settings
        update_data = {
            'desktop_notifications_enabled': False,
            'web_notifications_enabled': True
        }
        
        response = self.client.post(
            '/api/notifications/settings',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'message' in data
    
    def test_test_notifications_endpoint(self):
        """Test the test notifications API endpoint"""
        # Test status notification
        response = self.client.post(
            '/api/notifications/test',
            data=json.dumps({'type': 'status'}),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'status notification sent' in data['message']
        
        # Test property found notification
        response = self.client.post(
            '/api/notifications/test',
            data=json.dumps({'type': 'property_found'}),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'property found notification sent' in data['message']
        
        # Test property removed notification
        response = self.client.post(
            '/api/notifications/test',
            data=json.dumps({'type': 'property_removed'}),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'property removed notification sent' in data['message']
        
        # Test error notification
        response = self.client.post(
            '/api/notifications/test',
            data=json.dumps({'type': 'error'}),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'error notification sent' in data['message']
    
    def test_websocket_connection(self):
        """Test WebSocket connection and initial status"""
        # Test that WebSocket client can connect
        assert self.socketio_client.is_connected()
        
        # Get received messages from connection
        received = self.socketio_client.get_received()
        
        # Should have received initial status update
        status_updates = [msg for msg in received if msg['name'] == 'status_update']
        assert len(status_updates) > 0
        
        status_data = status_updates[0]['args'][0]
        assert 'type' in status_data
        assert status_data['type'] == 'status_update'
    
    def test_websocket_status_request(self):
        """Test WebSocket status request"""
        # Clear initial messages
        self.socketio_client.get_received()
        
        # Request status
        self.socketio_client.emit('request_status')
        
        # Get received messages
        received = self.socketio_client.get_received()
        
        # Should receive status update
        status_updates = [msg for msg in received if msg['name'] == 'status_update']
        assert len(status_updates) > 0
        
        status_data = status_updates[0]['args'][0]
        assert 'monitoring_active' in status_data
        assert 'monitoring_status' in status_data
    
    def test_websocket_test_notification(self):
        """Test WebSocket test notification"""
        # Clear initial messages
        self.socketio_client.get_received()
        
        # Send test notification request
        self.socketio_client.emit('test_notification')
        
        # Get received messages
        received = self.socketio_client.get_received()
        
        # Should receive test response and notification
        assert len(received) >= 1
        
        # Check for test response
        test_responses = [msg for msg in received if msg['name'] == 'test_response']
        assert len(test_responses) > 0
        
        test_response = test_responses[0]['args'][0]
        assert test_response['success'] is True
    
    @patch('shared.notification_manager.desktop_notification')
    def test_monitoring_event_handling(self, mock_desktop_notification):
        """Test that monitoring events trigger notifications"""
        # Mock WebSocket callback to capture notifications
        websocket_notifications = []
        
        def capture_websocket_notification(data):
            websocket_notifications.append(data)
        
        self.api.notification_manager.add_websocket_callback(capture_websocket_notification)
        self.api.notification_manager.desktop_notifications_enabled = True
        
        # Test property found event
        property_found_event = MonitoringEvent(
            event_type="property_found",
            timestamp=datetime.now(),
            message="Target property found: 123 Integration Test Street (MLS: INT123)",
            property_listing=self.test_listing
        )
        
        self.api._handle_monitoring_event(property_found_event)
        
        # Verify WebSocket notification was sent
        assert len(websocket_notifications) > 0
        notification = websocket_notifications[-1]
        assert notification['notification_type'] == 'property_found'
        assert notification['title'] == '🏠 Target Property Found!'
        assert 'property' in notification
        assert notification['property']['address'] == '123 Integration Test Street'
        
        # Verify desktop notification was called
        mock_desktop_notification.notify.assert_called()
        
        # Test property removed event
        websocket_notifications.clear()
        mock_desktop_notification.reset_mock()
        
        property_removed_event = MonitoringEvent(
            event_type="property_removed",
            timestamp=datetime.now(),
            message="Target property removed: INT123"
        )
        
        self.api._handle_monitoring_event(property_removed_event)
        
        # Verify WebSocket notification was sent
        assert len(websocket_notifications) > 0
        notification = websocket_notifications[-1]
        assert notification['notification_type'] == 'property_removed'
        assert notification['title'] == '🏠 Target Property Removed'
        
        # Test error event
        websocket_notifications.clear()
        mock_desktop_notification.reset_mock()
        
        error_event = MonitoringEvent(
            event_type="error",
            timestamp=datetime.now(),
            message="Network connection failed",
            error=ConnectionError("Timeout")
        )
        
        self.api._handle_monitoring_event(error_event)
        
        # Verify WebSocket notification was sent
        assert len(websocket_notifications) > 0
        notification = websocket_notifications[-1]
        assert notification['notification_type'] == 'error'
        assert notification['title'] == '⚠️ Monitoring Error'
        
        # Test status change event (desktop only)
        websocket_notifications.clear()
        mock_desktop_notification.reset_mock()
        
        status_event = MonitoringEvent(
            event_type="status_change",
            timestamp=datetime.now(),
            message="Monitoring service started"
        )
        
        self.api._handle_monitoring_event(status_event)
        
        # WebSocket notification should not be sent for status updates (desktop only)
        # But desktop notification should be sent
        mock_desktop_notification.notify.assert_called()
    
    def test_websocket_notification_format(self):
        """Test WebSocket notification format matches expected structure"""
        websocket_notifications = []
        
        def capture_websocket_notification(data):
            websocket_notifications.append(data)
        
        self.api.notification_manager.add_websocket_callback(capture_websocket_notification)
        
        # Send property found notification
        self.api.notification_manager.notify_property_found(self.test_listing)
        
        assert len(websocket_notifications) > 0
        notification = websocket_notifications[0]
        
        # Check required fields
        required_fields = ['type', 'notification_type', 'title', 'message', 'timestamp', 'data']
        for field in required_fields:
            assert field in notification, f"Missing field: {field}"
        
        # Check property data
        assert 'property' in notification
        property_data = notification['property']
        
        property_fields = [
            'address', 'price', 'mls_number', 'description', 'square_footage',
            'bedrooms', 'bathrooms', 'lot_size', 'year_built', 'image_url',
            'listing_url', 'first_seen'
        ]
        for field in property_fields:
            assert field in property_data, f"Missing property field: {field}"
        
        # Verify data types
        assert isinstance(notification['timestamp'], str)
        assert isinstance(notification['data'], dict)
        assert isinstance(property_data['bedrooms'], int)
        assert isinstance(property_data['bathrooms'], int)
    
    def test_notification_error_handling(self):
        """Test error handling in notification system"""
        # Test with empty notification settings (no JSON data)
        response = self.client.post(
            '/api/notifications/settings',
            content_type='application/json'
        )
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Invalid JSON data' in data['error']
        
        # Test with valid empty JSON
        response = self.client.post(
            '/api/notifications/settings',
            data=json.dumps({}),
            content_type='application/json'
        )
        # This should succeed but not change anything
        assert response.status_code == 200
        
        # Test with invalid JSON structure (but valid JSON)
        response = self.client.post(
            '/api/notifications/settings',
            data=json.dumps({"invalid_field": "value"}),
            content_type='application/json'
        )
        # This should succeed but not change anything
        assert response.status_code == 200
    
    def test_websocket_error_handling(self):
        """Test WebSocket error handling"""
        # Clear initial messages
        self.socketio_client.get_received()
        
        # Mock an error in status request
        with patch.object(self.api.config_manager, 'load_config', side_effect=Exception("Config error")):
            self.socketio_client.emit('request_status')
            
            # Get received messages
            received = self.socketio_client.get_received()
            
            # Should receive error message
            error_messages = [msg for msg in received if msg['name'] == 'error']
            assert len(error_messages) > 0
    
    def teardown_method(self):
        """Clean up after each test method"""
        if hasattr(self, 'socketio_client') and self.socketio_client.is_connected():
            self.socketio_client.disconnect()


if __name__ == '__main__':
    pytest.main([__file__])