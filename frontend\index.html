<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Estate Monitor</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/styles.css">
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">Real Estate Monitor</h1>
                <div class="header-controls">
                    <button class="btn btn-secondary" id="config-btn">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
                        </svg>
                        Settings
                    </button>
                    <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                        <svg class="icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"></circle>
                            <path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72l1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"></path>
                        </svg>
                        <svg class="icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Status Section -->
            <section class="status-section">
                <div class="status-card">
                    <div class="status-header">
                        <h2>Monitoring Status</h2>
                        <div class="status-indicator" id="status-indicator">
                            <div class="status-dot"></div>
                            <span class="status-text">Stopped</span>
                        </div>
                    </div>
                    <div class="status-details">
                        <div class="status-item">
                            <span class="label">Target Address:</span>
                            <span class="value" id="target-address">Not configured</span>
                        </div>
                        <div class="status-item">
                            <span class="label">Last Check:</span>
                            <span class="value" id="last-check">Never</span>
                        </div>
                        <div class="status-item">
                            <span class="label">Check Interval:</span>
                            <span class="value" id="check-interval">15 minutes</span>
                        </div>
                    </div>
                    <div class="status-actions">
                        <button class="btn btn-primary" id="start-monitoring">Start Monitoring</button>
                        <button class="btn btn-secondary" id="stop-monitoring" disabled>Stop Monitoring</button>
                        <button class="btn btn-outline" id="check-now">Check Now</button>
                    </div>
                </div>
            </section>

            <!-- Properties Section -->
            <section class="properties-section">
                <div class="section-header">
                    <h2>Found Properties</h2>
                    <div class="property-count">
                        <span id="property-count">0</span> properties found
                    </div>
                </div>
                <div class="properties-grid" id="properties-grid">
                    <!-- Property cards will be dynamically inserted here -->
                    <div class="empty-state" id="empty-state">
                        <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9,22 9,12 15,12 15,22"></polyline>
                        </svg>
                        <h3>No properties found yet</h3>
                        <p>Start monitoring to search for your target address</p>
                    </div>
                </div>
            </section>

            <!-- Activity Feed -->
            <section class="activity-section">
                <div class="section-header">
                    <h2>Activity Feed</h2>
                    <button class="btn btn-ghost" id="clear-activity">Clear</button>
                </div>
                <div class="activity-feed" id="activity-feed">
                    <div class="activity-item">
                        <div class="activity-time">Just now</div>
                        <div class="activity-message">Application started</div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Configuration Panel (Slide-over) -->
    <div class="config-overlay" id="config-overlay">
        <div class="config-panel">
            <div class="config-header">
                <h2>Configuration</h2>
                <button class="btn btn-ghost config-close" id="config-close">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <form class="config-form" id="config-form">
                <div class="form-group">
                    <label for="target-address-input">Target Address</label>
                    <input type="text" id="target-address-input" name="target_address" placeholder="e.g., 123 Main Street, City" required>
                    <small class="form-help">Enter the address you want to monitor</small>
                </div>
                <div class="form-group">
                    <label for="check-interval-input">Check Interval (minutes)</label>
                    <input type="number" id="check-interval-input" name="check_interval_minutes" min="1" max="1440" value="15" required>
                    <small class="form-help">How often to check for new listings</small>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="notifications-enabled" name="notifications_enabled" checked>
                        <span class="checkbox-custom"></span>
                        Enable notifications
                    </label>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="config-save">Save Configuration</button>
                    <button type="button" class="btn btn-secondary" id="config-cancel">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Property Card Template (Hidden) -->
    <template id="property-card-template">
        <div class="property-card">
            <div class="property-image">
                <img src="" alt="Property image" loading="lazy">
                <div class="property-status">New</div>
            </div>
            <div class="property-content">
                <div class="property-header">
                    <h3 class="property-address"></h3>
                    <div class="property-price"></div>
                </div>
                <div class="property-details">
                    <div class="property-meta">
                        <span class="meta-item">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            </svg>
                            <span class="bedrooms"></span> bed
                        </span>
                        <span class="meta-item">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 6l6 6l-6 6"></path>
                            </svg>
                            <span class="bathrooms"></span> bath
                        </span>
                        <span class="meta-item">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            </svg>
                            <span class="square-footage"></span> sq ft
                        </span>
                    </div>
                    <div class="property-mls">MLS: <span class="mls-number"></span></div>
                </div>
                <div class="property-description"></div>
                <div class="property-actions">
                    <a href="#" class="btn btn-primary property-link" target="_blank">View Listing</a>
                    <div class="property-timestamp">Found: <span class="timestamp"></span></div>
                </div>
            </div>
        </div>
    </template>

    <!-- Notification Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Connection Status Indicator -->
    <div class="connection-status" id="connection-status">
        <div class="status-dot"></div>
        <span class="status-text">Connecting...</span>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>