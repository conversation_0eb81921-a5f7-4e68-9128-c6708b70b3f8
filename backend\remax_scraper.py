"""
Web scraper for RE/MAX Canada real estate listings - inherits from BaseScraper.
"""
import requests
from bs4 import BeautifulSoup
from typing import List, Optional
import logging
import time
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse
import random

from shared.models import PropertyListing
from backend.base_scraper import BaseScraper


class RemaxScraper(BaseScraper):
    """Web scraper for RE/MAX Canada property listings"""
    
    def __init__(self, base_url: str = "https://www.remax.ca"):
        self.base_url = base_url
        self.listings_url = f"{base_url}/sk/meadow-lake-real-estate"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum 1 second between requests
        
        # Retry configuration
        self.max_retries = 3
        self.base_retry_delay = 1.0  # Base delay for exponential backoff
        self.max_retry_delay = 30.0  # Maximum retry delay
    
    def get_source_name(self) -> str:
        """Get the name of the data source"""
        return "remax.ca"
    
    def _rate_limit(self):
        """Implement rate limiting to be respectful to the website"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _handle_pagination(self) -> List[str]:
        """
        Fetch all pages of results from RE/MAX website
        
        Returns:
            List of HTML content from all pages
            
        Raises:
            requests.RequestException: For network-related errors after all retries
        """
        pages = []
        page_number = 1
        
        self.logger.info("Starting pagination handling for RE/MAX listings")
        
        while True:
            url = f"{self.listings_url}?lang=en&pageNumber={page_number}"
            self.logger.debug(f"Fetching page {page_number}: {url}")
            
            try:
                response = self._fetch_page(url)
                
                if self._is_empty_page(response.text):
                    self.logger.info(f"No more results found at page {page_number}, stopping pagination")
                    break
                    
                pages.append(response.text)
                self.logger.debug(f"Successfully fetched page {page_number}")
                page_number += 1
                
                # Rate limiting between page requests
                self._rate_limit()
                
            except requests.RequestException as e:
                self.logger.error(f"Failed to fetch page {page_number} after all retries: {e}")
                # Stop pagination on persistent errors to avoid infinite loops
                break
        
        self.logger.info(f"Pagination complete. Fetched {len(pages)} pages total")
        return pages
    
    def _fetch_page(self, url: str) -> requests.Response:
        """
        Fetch a single page with retry logic and exponential backoff
        
        Args:
            url: The URL to fetch
            
        Returns:
            Response object
            
        Raises:
            requests.RequestException: After all retries are exhausted
        """
        for attempt in range(self.max_retries):
            try:
                self._rate_limit()
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response
                
            except requests.RequestException as e:
                self.logger.warning(f"Request attempt {attempt + 1} failed for {url}: {e}")
                
                if attempt < self.max_retries - 1:
                    self._exponential_backoff(attempt)
                else:
                    self.logger.error(f"All {self.max_retries} attempts failed for {url}")
                    raise
    
    def _is_empty_page(self, html: str) -> bool:
        """
        Detect if a page has no property listings (indicating end of pagination)
        
        Args:
            html: HTML content of the page
            
        Returns:
            True if page is empty/has no listings, False otherwise
        """
        if not html or len(html.strip()) == 0:
            return True
            
        soup = BeautifulSoup(html, 'html.parser')
        
        # Look for common indicators of empty pages
        # These selectors will need to be refined based on actual RE/MAX HTML structure
        
        # Check for explicit "no results" messages
        no_results_indicators = [
            'no results found',
            'no properties found',
            'no listings available',
            '0 results',
            'no matches'
        ]
        
        page_text = soup.get_text().lower()
        for indicator in no_results_indicators:
            if indicator in page_text:
                self.logger.debug(f"Found empty page indicator: '{indicator}'")
                return True
        
        # Check for property listing containers
        # Common selectors that might contain property listings
        property_selectors = [
            '.property-listing',
            '.listing-item',
            '.property-card',
            '.search-result',
            '[data-listing-id]',
            '.property-container'
        ]
        
        for selector in property_selectors:
            properties = soup.select(selector)
            if properties:
                self.logger.debug(f"Found {len(properties)} properties using selector '{selector}'")
                return False
        
        # If page is very small, it's likely empty
        if len(html.strip()) < 1000:  # Arbitrary threshold
            self.logger.debug("Page content is very small, likely empty")
            return True
        
        # Default to not empty if we can't determine definitively
        # This errs on the side of caution to avoid missing listings
        self.logger.debug("Could not definitively determine if page is empty, assuming not empty")
        return False

    def fetch_listings(self) -> List[PropertyListing]:
        """
        Fetch and parse all property listings from RE/MAX with retry logic
        
        Returns:
            List of PropertyListing objects
            
        Raises:
            requests.RequestException: For network-related errors after all retries
            ValueError: For parsing errors
        """
        # Placeholder implementation - will be implemented in later tasks
        # For now, return empty list to satisfy the interface
        self.logger.info("RE/MAX scraper fetch_listings called - implementation pending")
        return []
    
    def _exponential_backoff(self, attempt: int):
        """
        Implement exponential backoff with jitter for retry delays
        
        Args:
            attempt: Current attempt number (0-based)
        """
        # Calculate exponential backoff: base_delay * (2 ^ attempt)
        delay = min(self.base_retry_delay * (2 ** attempt), self.max_retry_delay)
        
        # Add jitter to avoid thundering herd problem
        jitter = random.uniform(0.1, 0.5) * delay
        total_delay = delay + jitter
        
        self.logger.info(f"Waiting {total_delay:.2f} seconds before retry...")
        time.sleep(total_delay)