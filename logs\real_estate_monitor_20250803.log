2025-08-03 21:43:52 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:43:52 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:43:52 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:43:52 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:43:52 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:43:52 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:43:52 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:44:42 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:44:42 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:44:42 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5001
2025-08-03 21:44:43 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:44:43 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 21:44:44 - real_estate_monitor - INFO - <PERSON><PERSON>er opening disabled by --no-browser flag
2025-08-03 21:44:48 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:45:24 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:45:24 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:45:24 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:45:24 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:46:23 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:46:23 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:46:23 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5003
2025-08-03 21:46:25 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 21:46:25 - real_estate_monitor - ERROR - Failed to start application: 'charmap' codec can't encode character '\u2713' in position 2: character maps to <undefined>
2025-08-03 21:47:06 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:47:06 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:47:06 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5000
2025-08-03 21:47:08 - real_estate_monitor - INFO - Opening browser to http://127.0.0.1:5000
2025-08-03 21:48:08 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:48:08 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:48:08 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5000
2025-08-03 21:48:10 - real_estate_monitor - INFO - Opening browser to http://127.0.0.1:5000
2025-08-03 21:49:06 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:49:06 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:49:06 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5004
2025-08-03 21:49:08 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 21:51:14 - real_estate_monitor - INFO - Received signal 2, initiating graceful shutdown...
2025-08-03 21:51:14 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:51:14 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 21:51:19 - real_estate_monitor - INFO - Received signal 2, initiating graceful shutdown...
2025-08-03 21:51:19 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:51:19 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 21:51:24 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:53:33 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:53:33 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:53:33 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5005
2025-08-03 21:53:33 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:53:33 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 21:53:35 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 21:53:38 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:53:56 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:53:56 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:53:56 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5005
2025-08-03 21:53:57 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:53:57 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 21:53:58 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 21:54:02 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:54:22 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:54:22 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:54:22 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5005
2025-08-03 21:54:23 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 21:54:23 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 21:54:24 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 21:54:28 - real_estate_monitor - INFO - Application shutdown complete
2025-08-03 21:55:11 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 21:55:11 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 21:55:11 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5000
2025-08-03 21:55:13 - real_estate_monitor - INFO - Opening browser to http://127.0.0.1:5000
2025-08-03 22:04:07 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 22:04:07 - real_estate_monitor - INFO - First-time setup required.
2025-08-03 22:04:07 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5000
2025-08-03 22:04:07 - real_estate_monitor - ERROR - Failed to start API server: The Werkzeug web server is not designed to run in production. Pass allow_unsafe_werkzeug=True to the run() method to disable this error.
2025-08-03 22:04:09 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 22:04:09 - real_estate_monitor - ERROR - Failed to start application: 'charmap' codec can't encode character '\u2713' in position 2: character maps to <undefined>
2025-08-03 22:09:03 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 22:09:03 - real_estate_monitor - INFO - Configuration loaded. Target address: 456 Curl Test Avenue
2025-08-03 22:09:03 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5000
2025-08-03 22:09:03 - real_estate_monitor - ERROR - Failed to start API server: [Errno 22] Invalid argument
2025-08-03 22:09:05 - real_estate_monitor - INFO - Browser opening disabled by --no-browser flag
2025-08-03 22:09:05 - real_estate_monitor - ERROR - Failed to start application: 'charmap' codec can't encode character '\u2713' in position 2: character maps to <undefined>
2025-08-03 22:10:57 - real_estate_monitor - INFO - Logging configuration initialized
2025-08-03 22:10:57 - real_estate_monitor - INFO - Configuration loaded. Target address: 456 Curl Test Avenue
2025-08-03 22:10:57 - real_estate_monitor - INFO - Starting API server on 127.0.0.1:5000
2025-08-03 22:10:59 - real_estate_monitor - INFO - Opening browser to http://127.0.0.1:5000
2025-08-03 22:18:36 - real_estate_monitor - INFO - Received signal 2, initiating graceful shutdown...
2025-08-03 22:18:36 - real_estate_monitor - INFO - Shutting down Real Estate Monitor application...
2025-08-03 22:18:36 - real_estate_monitor - INFO - Waiting for server thread to finish...
2025-08-03 22:18:41 - real_estate_monitor - INFO - Application shutdown complete
