#!/usr/bin/env python3
"""
Inspect the HTML structure of meadownorth.ca to understand why parsing is failing
"""
import requests
from bs4 import BeautifulSoup
import re

def inspect_website():
    """Inspect the website HTML structure"""
    url = "https://meadownorth.ca/listings.html"
    
    print(f"🔍 Inspecting HTML structure of: {url}")
    print("=" * 60)
    
    try:
        # Fetch the page
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"✅ Successfully fetched page (status: {response.status_code})")
        print(f"📄 Content length: {len(response.text)} characters")
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Look for the current scraper's selectors
        print(f"\n🔍 Testing current scraper selectors:")
        
        # Current selector: elements with class containing 'listing', 'property', or 'card'
        property_elements = soup.find_all(['div', 'article'], class_=re.compile(r'(listing|property|card)', re.I))
        print(f"Elements with class containing 'listing|property|card': {len(property_elements)}")
        
        # Alternative selector: elements with data-listing attribute
        data_listing_elements = soup.find_all(['div'], attrs={'data-listing': True})
        print(f"Elements with data-listing attribute: {len(data_listing_elements)}")
        
        # Look for price patterns in text
        price_elements = soup.find_all(['div'], string=re.compile(r'\$[\d,]+', re.I))
        print(f"Elements containing price patterns: {len(price_elements)}")
        
        # Show page title and basic structure
        title = soup.find('title')
        print(f"\n📋 Page title: {title.get_text() if title else 'Not found'}")
        
        # Look for common real estate listing patterns
        print(f"\n🏠 Looking for common real estate patterns:")
        
        # Search for addresses
        address_patterns = [
            r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd|Circle|Cir|Court|Ct|Place|Pl)',
            r'\d+\s+[A-Za-z\s]+(?:ST|AVE|RD|DR|LN|BLVD|CIR|CT|PL)'
        ]
        
        page_text = soup.get_text()
        addresses_found = []
        
        for pattern in address_patterns:
            matches = re.findall(pattern, page_text, re.I)
            addresses_found.extend(matches)
        
        print(f"Addresses found in page text: {len(addresses_found)}")
        for addr in addresses_found[:10]:  # Show first 10
            print(f"  - {addr}")
        
        # Look specifically for "810 Centre"
        centre_matches = re.findall(r'810\s+Centre\s+\w+', page_text, re.I)
        print(f"\n🎯 '810 Centre' matches: {len(centre_matches)}")
        for match in centre_matches:
            print(f"  - {match}")
        
        # Look for any mention of "810"
        num_810_matches = re.findall(r'810[^0-9]*[A-Za-z\s]+', page_text, re.I)
        print(f"\n🏠 '810' mentions: {len(num_810_matches)}")
        for match in num_810_matches[:5]:  # Show first 5
            print(f"  - {match.strip()}")
        
        # Show the HTML structure around potential listings
        print(f"\n🔧 HTML structure analysis:")
        
        # Look for divs that might contain listings
        all_divs = soup.find_all('div')
        print(f"Total div elements: {len(all_divs)}")
        
        # Find divs with text content that looks like listings
        potential_listing_divs = []
        for div in all_divs:
            text = div.get_text().strip()
            if len(text) > 50 and ('$' in text or 'street' in text.lower() or 'avenue' in text.lower()):
                potential_listing_divs.append(div)
        
        print(f"Potential listing divs: {len(potential_listing_divs)}")
        
        # Show the first few potential listing divs
        for i, div in enumerate(potential_listing_divs[:3]):
            print(f"\n--- Potential Listing {i+1} ---")
            print(f"Classes: {div.get('class', [])}")
            print(f"ID: {div.get('id', 'None')}")
            print(f"Text preview: {div.get_text()[:200]}...")
            
            # Show the HTML structure
            print(f"HTML: {str(div)[:300]}...")
        
        # Save a sample of the HTML for manual inspection
        with open('meadownorth_sample.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"\n💾 Full HTML saved to 'meadownorth_sample.html' for manual inspection")
        
    except Exception as e:
        print(f"❌ Error inspecting website: {str(e)}")

if __name__ == "__main__":
    inspect_website()