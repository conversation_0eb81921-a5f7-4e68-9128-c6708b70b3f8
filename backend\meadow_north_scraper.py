"""
Web scraper for meadownorth.ca real estate listings - inherits from BaseScraper.
"""
import requests
from bs4 import BeautifulSoup
from typing import List, Optional
import logging
import time
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse
import random

from shared.models import PropertyListing
from backend.base_scraper import BaseScraper


class MeadowNorthScraper(BaseScraper):
    """Web scraper for meadownorth.ca property listings"""
    
    def __init__(self, base_url: str = "https://meadownorth.ca"):
        self.base_url = base_url
        self.listings_url = f"{base_url}/listings.html"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum 1 second between requests
        
        # Retry configuration
        self.max_retries = 3
        self.base_retry_delay = 1.0  # Base delay for exponential backoff
        self.max_retry_delay = 30.0  # Maximum retry delay
    
    def get_source_name(self) -> str:
        """Get the name of the data source"""
        return "meadownorth.ca"
    
    def _rate_limit(self):
        """Implement rate limiting to be respectful to the website"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def fetch_listings(self) -> List[PropertyListing]:
        """
        Fetch and parse all property listings from meadownorth.ca with retry logic
        
        Returns:
            List of PropertyListing objects
            
        Raises:
            requests.RequestException: For network-related errors after all retries
            ValueError: For parsing errors
        """
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                self._rate_limit()
                
                self.logger.info(f"Fetching listings from {self.listings_url} (attempt {attempt + 1}/{self.max_retries})")
                response = self.session.get(self.listings_url, timeout=30)
                response.raise_for_status()
                
                return self.parse_listing_html(response.text)
                
            except requests.exceptions.Timeout as e:
                last_exception = e
                self.logger.warning(f"Request timed out on attempt {attempt + 1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    self._exponential_backoff(attempt)
                    continue
                    
            except requests.exceptions.ConnectionError as e:
                last_exception = e
                self.logger.warning(f"Connection error on attempt {attempt + 1}/{self.max_retries}: {str(e)}")
                if attempt < self.max_retries - 1:
                    self._exponential_backoff(attempt)
                    continue
                    
            except requests.exceptions.HTTPError as e:
                last_exception = e
                status_code = e.response.status_code if e.response else "unknown"
                self.logger.warning(f"HTTP error {status_code} on attempt {attempt + 1}/{self.max_retries}")
                
                # Don't retry on client errors (4xx), but do retry on server errors (5xx)
                if e.response and 400 <= e.response.status_code < 500:
                    self.logger.error(f"Client error {status_code}, not retrying")
                    break
                elif attempt < self.max_retries - 1:
                    self._exponential_backoff(attempt)
                    continue
                    
            except Exception as e:
                last_exception = e
                self.logger.error(f"Unexpected error on attempt {attempt + 1}/{self.max_retries}: {str(e)}")
                if attempt < self.max_retries - 1:
                    self._exponential_backoff(attempt)
                    continue
                else:
                    break
        
        # All retries failed
        error_msg = f"Failed to fetch listings after {self.max_retries} attempts"
        if last_exception:
            error_msg += f": {str(last_exception)}"
        
        self.logger.error(error_msg)
        raise requests.RequestException(error_msg)
    
    def _exponential_backoff(self, attempt: int):
        """
        Implement exponential backoff with jitter for retry delays
        
        Args:
            attempt: Current attempt number (0-based)
        """
        # Calculate exponential backoff: base_delay * (2 ^ attempt)
        delay = min(self.base_retry_delay * (2 ** attempt), self.max_retry_delay)
        
        # Add jitter to avoid thundering herd problem
        jitter = random.uniform(0.1, 0.5) * delay
        total_delay = delay + jitter
        
        self.logger.info(f"Waiting {total_delay:.2f} seconds before retry...")
        time.sleep(total_delay)
    
    def parse_listing_html(self, html: str) -> List[PropertyListing]:
        """
        Parse HTML content to extract property listings
        
        Args:
            html: Raw HTML content from the listings page
            
        Returns:
            List of PropertyListing objects
            
        Raises:
            ValueError: If HTML parsing fails
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')
            listings = []
            
            # Look for property listing containers
            # Based on meadownorth.ca structure: div with class "results_single fluid"
            property_elements = soup.find_all('div', class_='results_single fluid')
            
            if not property_elements:
                # Fallback: Try alternative selectors
                property_elements = soup.find_all(['div', 'article'], class_=re.compile(r'(listing|property|card)', re.I))
                
            if not property_elements:
                # Try alternative selectors
                property_elements = soup.find_all(['div'], attrs={'data-listing': True})
                
            if not property_elements:
                # Try finding by common patterns
                property_elements = soup.find_all(['div'], string=re.compile(r'\$[\d,]+', re.I))
                if property_elements:
                    property_elements = [elem.parent for elem in property_elements if elem.parent]
            
            self.logger.info(f"Found {len(property_elements)} potential property elements")
            
            for element in property_elements:
                try:
                    listing = self.extract_property_data(element)
                    if listing:
                        listings.append(listing)
                except Exception as e:
                    self.logger.warning(f"Failed to parse property element: {str(e)}")
                    continue
            
            self.logger.info(f"Successfully parsed {len(listings)} property listings")
            return listings
            
        except Exception as e:
            # Only raise ValueError for truly invalid HTML, not for empty results
            if "invalid" in str(e).lower() or "malformed" in str(e).lower():
                self.logger.error(f"Failed to parse HTML: {str(e)}")
                raise ValueError(f"HTML parsing failed: {str(e)}")
            else:
                self.logger.warning(f"HTML parsing completed with issues: {str(e)}")
                return listings
    
    def extract_property_data(self, element) -> Optional[PropertyListing]:
        """
        Extract property data from a single HTML element
        
        Args:
            element: BeautifulSoup element containing property data
            
        Returns:
            PropertyListing object or None if extraction fails
        """
        try:
            # Extract price
            price = self._extract_price(element)
            if not price:
                return None
            
            # Extract address
            address = self._extract_address(element)
            if not address:
                return None
            
            # Extract MLS number
            mls_number = self._extract_mls_number(element)
            if not mls_number:
                mls_number = f"UNKNOWN_{int(time.time())}"
            
            # Extract description
            description = self._extract_description(element)
            
            # Extract optional fields
            square_footage = self._extract_square_footage(element)
            bedrooms = self._extract_bedrooms(element)
            bathrooms = self._extract_bathrooms(element)
            lot_size = self._extract_lot_size(element)
            year_built = self._extract_year_built(element)
            image_url = self._extract_image_url(element)
            listing_url = self._extract_listing_url(element)
            
            return PropertyListing(
                address=address,
                price=price,
                mls_number=mls_number,
                description=description,
                square_footage=square_footage,
                bedrooms=bedrooms,
                bathrooms=bathrooms,
                lot_size=lot_size,
                year_built=year_built,
                image_url=image_url,
                listing_url=listing_url,
                first_seen=datetime.now()
            )
            
        except Exception as e:
            self.logger.warning(f"Failed to extract property data: {str(e)}")
            return None 
   
    def _extract_price(self, element) -> Optional[str]:
        """Extract price from property element"""
        # Look for price in rs_price div structure
        price_div = element.find('div', class_='rs_price')
        if price_div:
            price_span = price_div.find('span')
            if price_span and price_span.get_text().strip():
                price_text = price_span.get_text().strip()
                # Clean up the price
                if price_text.startswith('$'):
                    return price_text
                elif price_text.isdigit():
                    return f"${price_text}"
        
        # Fallback: Look for price patterns
        price_patterns = [
            r'\$[\d,]+',
            r'Price[:\s]*\$?[\d,]+',
            r'[\d,]+\s*dollars?'
        ]
        
        text = element.get_text()
        for pattern in price_patterns:
            match = re.search(pattern, text, re.I)
            if match:
                price = match.group(0)
                # Clean up the price
                price = re.sub(r'[^\d$,]', '', price)
                if '$' not in price and price.isdigit():
                    price = f"${price}"
                return price
        
        # Look for price in specific elements
        price_elem = element.find(['span', 'div', 'p'], class_=re.compile(r'price', re.I))
        if price_elem:
            return price_elem.get_text().strip()
        
        return None
    
    def _extract_address(self, element) -> Optional[str]:
        """Extract address from property element"""
        # Look for address in rs_price div structure (it's after the price span)
        price_div = element.find('div', class_='rs_price')
        if price_div:
            price_p = price_div.find('p')
            if price_p:
                # Address is typically after the price span
                text = price_p.get_text()
                # Remove the price part and extract address
                lines = text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('$') and not line.replace(',', '').replace('$', '').isdigit():
                        # This looks like an address
                        return line.strip()
        
        # Fallback: Look for address patterns
        address_patterns = [
            r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd|Circle|Cir|Court|Ct|Place|Pl)',
            r'\d+\s+[A-Za-z\s]+(?:ST|AVE|RD|DR|LN|BLVD|CIR|CT|PL|STREET)'
        ]
        
        text = element.get_text()
        for pattern in address_patterns:
            match = re.search(pattern, text, re.I)
            if match:
                return match.group(0).strip()
        
        # Look for address in specific elements
        address_elem = element.find(['span', 'div', 'p'], class_=re.compile(r'address', re.I))
        if address_elem:
            return address_elem.get_text().strip()
        
        return None
    
    def _extract_mls_number(self, element) -> Optional[str]:
        """Extract MLS number from property element"""
        # Look for MLS in rs_number span
        mls_span = element.find('span', class_='rs_number')
        if mls_span:
            mls_text = mls_span.get_text()
            # Extract the MLS number from "MLS#: SK004408" format
            match = re.search(r'MLS#:\s*([A-Z0-9]+)', mls_text, re.I)
            if match:
                return match.group(1)
        
        # Fallback: Look for MLS patterns
        mls_patterns = [
            r'MLS[#:\s]*([A-Z0-9]+)',
            r'MLS[#:\s]*(\d+)',
            r'#([A-Z0-9]+)'
        ]
        
        text = element.get_text()
        for pattern in mls_patterns:
            match = re.search(pattern, text, re.I)
            if match:
                return match.group(1)
        
        # Look for MLS in specific elements
        mls_elem = element.find(['span', 'div', 'p'], class_=re.compile(r'mls', re.I))
        if mls_elem:
            return mls_elem.get_text().strip()
        
        return None
    
    def _extract_description(self, element) -> str:
        """Extract description from property element"""
        # Look for description in rs_details div
        details_div = element.find('div', class_='rs_details')
        if details_div:
            desc_p = details_div.find('p')
            if desc_p:
                desc_text = desc_p.get_text().strip()
                # Remove the "...more" link text if present
                desc_text = re.sub(r'\s*\.\.\.\s*more\s*$', '', desc_text)
                return desc_text
        
        # Look for description in specific elements
        desc_elem = element.find(['div', 'p'], class_=re.compile(r'(description|summary|details)', re.I))
        if desc_elem:
            return desc_elem.get_text().strip()
        
        # Fallback to element text, but limit length
        text = element.get_text().strip()
        if len(text) > 500:
            text = text[:500] + "..."
        
        return text
    
    def _extract_square_footage(self, element) -> Optional[str]:
        """Extract square footage from property element"""
        patterns = [
            r'(\d+[\d,]*)\s*(?:sq\.?\s*ft\.?|square\s*feet)',
            r'(\d+[\d,]*)\s*sf'
        ]
        
        text = element.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.I)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_bedrooms(self, element) -> Optional[int]:
        """Extract number of bedrooms from property element"""
        patterns = [
            r'(\d+)\s*(?:bed|bedroom|br)',
            r'(\d+)\s*bd'
        ]
        
        text = element.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.I)
            if match:
                try:
                    return int(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def _extract_bathrooms(self, element) -> Optional[int]:
        """Extract number of bathrooms from property element"""
        patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:bath|bathroom|ba)',
            r'(\d+(?:\.\d+)?)\s*ba'
        ]
        
        text = element.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.I)
            if match:
                try:
                    return int(float(match.group(1)))
                except ValueError:
                    continue
        
        return None
    
    def _extract_lot_size(self, element) -> Optional[str]:
        """Extract lot size from property element"""
        patterns = [
            r'(\d+[\d,]*\.?\d*)\s*(?:acres?|ac)',
            r'(\d+[\d,]*)\s*(?:sq\.?\s*ft\.?)\s*lot'
        ]
        
        text = element.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.I)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_year_built(self, element) -> Optional[str]:
        """Extract year built from property element"""
        patterns = [
            r'(?:built|year)[:\s]*(\d{4})',
            r'(\d{4})\s*built',
            r'built\s+in\s+(\d{4})'
        ]
        
        text = element.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.I)
            if match:
                year = int(match.group(1))
                if 1800 <= year <= datetime.now().year:
                    return str(year)
        
        return None
    
    def _extract_image_url(self, element) -> str:
        """Extract image URL from property element"""
        # Look for images
        img_elem = element.find('img')
        if img_elem and img_elem.get('src'):
            src = img_elem.get('src')
            # Convert relative URLs to absolute
            if src.startswith('/'):
                return urljoin(self.base_url, src)
            elif src.startswith('http'):
                return src
        
        return ""
    
    def _extract_listing_url(self, element) -> str:
        """Extract listing URL from property element"""
        # Look for links
        link_elem = element.find('a', href=True)
        if link_elem:
            href = link_elem.get('href')
            # Convert relative URLs to absolute
            if href.startswith('/'):
                return urljoin(self.base_url, href)
            elif href.startswith('http'):
                return href
        
        return ""