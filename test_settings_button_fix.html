<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .config-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.25s ease-in-out;
        }
        .config-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .config-panel {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            max-width: 400px;
            height: 100%;
            background-color: white;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.25s ease-in-out;
            overflow-y: auto;
            padding: 20px;
        }
        .config-overlay.active .config-panel {
            transform: translateX(0);
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary { background-color: #0D7377; color: white; }
        .btn-secondary { background-color: #F4A460; color: white; }
        #console-output {
            background: #f5f5f5;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Settings Button Test</h1>
    <p>This page tests the settings button functionality with enhanced logging.</p>
    
    <button class="btn btn-secondary" id="config-btn">Settings</button>
    <button class="btn btn-primary" onclick="testConfigPanel()">Manual Test</button>
    <button class="btn btn-primary" onclick="debugConfigElements()">Debug Elements</button>
    
    <div id="console-output"></div>
    
    <!-- Configuration Panel -->
    <div class="config-overlay" id="config-overlay">
        <div class="config-panel">
            <h2>Configuration</h2>
            <button class="btn btn-secondary" id="config-close">Close</button>
            <button class="btn btn-secondary" id="config-cancel">Cancel</button>
            <p>This is the configuration panel. It should slide in from the right when the settings button is clicked.</p>
        </div>
    </div>

    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function logToPage(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog.apply(console, args);
            logToPage('info', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn.apply(console, args);
            logToPage('warn', ...args);
        };
        
        console.error = (...args) => {
            originalError.apply(console, args);
            logToPage('error', ...args);
        };
        
        // Simplified version of the RealEstateMonitor class for testing
        class TestMonitor {
            constructor() {
                this.logger = {
                    info: (msg, ...args) => console.log(`[INFO] ${msg}`, ...args),
                    warn: (msg, ...args) => console.warn(`[WARN] ${msg}`, ...args),
                    error: (msg, ...args) => console.error(`[ERROR] ${msg}`, ...args),
                    debug: (msg, ...args) => console.debug(`[DEBUG] ${msg}`, ...args)
                };
                
                this.logger.info('TestMonitor constructor called');
                this.logger.info('Document readyState:', document.readyState);
                
                if (document.readyState === 'loading') {
                    this.logger.info('DOM still loading, waiting for DOMContentLoaded...');
                    document.addEventListener('DOMContentLoaded', () => {
                        this.logger.info('DOMContentLoaded fired, initializing...');
                        this.setupConfigPanel();
                    });
                } else {
                    this.logger.info('DOM already ready, initializing immediately...');
                    this.setupConfigPanel();
                }
            }
            
            setupConfigPanel() {
                this.logger.info('Setting up configuration panel...');
                this.logger.info('DOM readyState:', document.readyState);
                this.logger.info('Document body exists:', !!document.body);
                
                if (document.readyState === 'loading') {
                    this.logger.info('DOM still loading, waiting for DOMContentLoaded...');
                    document.addEventListener('DOMContentLoaded', () => {
                        this.logger.info('DOMContentLoaded fired, retrying setupConfigPanel');
                        this._setupConfigPanelElements();
                    });
                } else {
                    this._setupConfigPanelElements();
                }
            }

            _setupConfigPanelElements() {
                this.logger.info('Setting up config panel elements...');
                
                const configBtn = document.getElementById('config-btn');
                const configOverlay = document.getElementById('config-overlay');
                const configClose = document.getElementById('config-close');
                const configCancel = document.getElementById('config-cancel');

                this.logger.info(`Found elements: btn=${!!configBtn}, overlay=${!!configOverlay}, close=${!!configClose}, cancel=${!!configCancel}`);
                
                if (configBtn) {
                    this.logger.info('Config button details:', {
                        id: configBtn.id,
                        className: configBtn.className,
                        tagName: configBtn.tagName,
                        parentElement: configBtn.parentElement?.tagName
                    });
                } else {
                    this.logger.error('Config button not found! Available elements with config in ID:');
                    const configElements = document.querySelectorAll('[id*="config"]');
                    configElements.forEach(el => {
                        this.logger.info(`- Found element: ${el.tagName}#${el.id}`);
                    });
                }

                if (configBtn) {
                    this.logger.info('Adding click listener to config button');
                    
                    const newConfigBtn = configBtn.cloneNode(true);
                    configBtn.parentNode.replaceChild(newConfigBtn, configBtn);
                    
                    newConfigBtn.addEventListener('click', (e) => {
                        this.logger.info('Config button clicked!', e);
                        e.preventDefault();
                        e.stopPropagation();
                        this.openConfigPanel();
                    });
                    
                    this.logger.info('Testing config button click handler...');
                    setTimeout(() => {
                        this.logger.info('Config button test - element still exists:', !!document.getElementById('config-btn'));
                    }, 100);
                } else {
                    this.logger.error('Config button not found!');
                }

                if (configClose) {
                    this.logger.info('Adding click listener to close button');
                    configClose.addEventListener('click', () => {
                        this.logger.info('Close button clicked');
                        this.closeConfigPanel();
                    });
                } else {
                    this.logger.warn('Config close button not found');
                }

                if (configCancel) {
                    this.logger.info('Adding click listener to cancel button');
                    configCancel.addEventListener('click', () => {
                        this.logger.info('Cancel button clicked');
                        this.closeConfigPanel();
                    });
                } else {
                    this.logger.warn('Config cancel button not found');
                }

                if (configOverlay) {
                    this.logger.info('Adding click listener to overlay');
                    configOverlay.addEventListener('click', (e) => {
                        if (e.target === configOverlay) {
                            this.logger.info('Overlay background clicked');
                            this.closeConfigPanel();
                        }
                    });
                } else {
                    this.logger.warn('Config overlay not found');
                }
                
                this.logger.info('Configuration panel setup completed');
                
                window.testConfigPanel = () => {
                    this.logger.info('Manual test of config panel triggered');
                    this.openConfigPanel();
                };
                
                window.debugConfigElements = () => {
                    const btn = document.getElementById('config-btn');
                    const overlay = document.getElementById('config-overlay');
                    this.logger.info('Debug - Config button:', btn);
                    this.logger.info('Debug - Config overlay:', overlay);
                    this.logger.info('Debug - All elements with config in ID:', 
                        Array.from(document.querySelectorAll('[id*="config"]')).map(el => `${el.tagName}#${el.id}`)
                    );
                };
            }
            
            openConfigPanel() {
                this.logger.info('openConfigPanel() called');
                const configOverlay = document.getElementById('config-overlay');
                if (configOverlay) {
                    this.logger.info('Config overlay found, current classes:', configOverlay.className);
                    this.logger.info('Current overlay styles:', {
                        display: getComputedStyle(configOverlay).display,
                        visibility: getComputedStyle(configOverlay).visibility,
                        opacity: getComputedStyle(configOverlay).opacity
                    });
                    
                    this.logger.info('Adding active class to overlay');
                    configOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    
                    this.logger.info('After adding active class:', configOverlay.className);
                    this.logger.info('Updated overlay styles:', {
                        display: getComputedStyle(configOverlay).display,
                        visibility: getComputedStyle(configOverlay).visibility,
                        opacity: getComputedStyle(configOverlay).opacity
                    });
                    
                    this.logger.info('Config panel should now be visible');
                } else {
                    this.logger.error('Config overlay not found in openConfigPanel!');
                    const overlayByClass = document.querySelector('.config-overlay');
                    if (overlayByClass) {
                        this.logger.info('Found overlay by class instead of ID:', overlayByClass);
                    } else {
                        this.logger.error('No config overlay found by class either');
                    }
                }
            }

            closeConfigPanel() {
                this.logger.info('closeConfigPanel() called');
                const configOverlay = document.getElementById('config-overlay');
                if (configOverlay) {
                    this.logger.info('Config overlay found, current classes:', configOverlay.className);
                    this.logger.info('Removing active class from overlay');
                    configOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    
                    this.logger.info('After removing active class:', configOverlay.className);
                    this.logger.info('Config panel should now be hidden');
                } else {
                    this.logger.error('Config overlay not found in closeConfigPanel!');
                }
            }
        }
        
        // Initialize the test monitor
        const monitor = new TestMonitor();
    </script>
</body>
</html>