#!/usr/bin/env python3
"""
End-to-end integration tests for the Real Estate Monitor application.
Tests the complete system integration including frontend-backend communication,
monitoring service, and error handling.
"""
import pytest
import threading
import time
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import requests

from backend.app import RealEstateMonitorAPI
from shared.config_manager import ConfigManager
from shared.models import Config, PropertyListing
from backend.scraper import MeadowNorthScraper
from backend.monitoring_service import MonitoringService
from shared.address_matcher import AddressMatcher
from shared.notification_manager import NotificationManager


class TestEndToEndIntegration:
    """End-to-end integration tests for the complete application"""
    
    def setup_method(self):
        """Set up test environment for each test"""
        # Create temporary directory for test data
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'config.json')
        
        # Create test configuration
        self.test_config = Config(
            target_address="123 Test Street",
            check_interval_minutes=1,  # Normal interval for testing
            notification_enabled=True
        )
        
        # Initialize components with test configuration
        self.config_manager = ConfigManager(config_file=self.config_file)
        self.config_manager.save_config(self.test_config)
        
        # Create mock scraper for controlled testing
        self.scraper = Mock(spec=MeadowNorthScraper)
        self.address_matcher = AddressMatcher()
        
        # Create test property listings
        self.test_listings = [
            PropertyListing(
                address="123 Test Street",
                price="$500,000",
                mls_number="TEST001",
                description="Test property 1",
                bedrooms=3,
                bathrooms=2,
                square_footage="1,500 sq ft"
            ),
            PropertyListing(
                address="456 Other Avenue",
                price="$600,000",
                mls_number="TEST002",
                description="Test property 2",
                bedrooms=4,
                bathrooms=3,
                square_footage="2,000 sq ft"
            )
        ]
        
        # Configure mock scraper
        self.scraper.fetch_listings.return_value = self.test_listings
        
        # Initialize API server
        self.api = RealEstateMonitorAPI()
        self.api.config_manager = self.config_manager
        self.api.scraper = self.scraper
        self.api.address_matcher = self.address_matcher
        
        # Reinitialize monitoring service with test components
        self.api.monitoring_service = MonitoringService(
            self.config_manager,
            self.scraper,
            self.api.address_matcher
        )
        self.api.monitoring_service.add_event_callback(self.api._handle_monitoring_event)
        
        # Start Flask test client
        self.api.app.config['TESTING'] = True
        self.client = self.api.app.test_client()
        
        # Track events for testing
        self.received_events = []
        self.original_emit_event = self.api.monitoring_service._emit_event
        self.api.monitoring_service._emit_event = self._capture_event
    
    def teardown_method(self):
        """Clean up after each test"""
        # Stop monitoring service if running
        if self.api.monitoring_service.is_running():
            self.api.monitoring_service.stop_monitoring(timeout=5.0)
        
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _capture_event(self, event):
        """Capture monitoring events for testing"""
        self.received_events.append(event)
        # Also call the original method
        self.original_emit_event(event)
    
    def test_complete_monitoring_workflow(self):
        """Test the complete monitoring workflow from start to finish"""
        # 1. Test initial configuration
        response = self.client.get('/api/config')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert data['data']['target_address'] == "123 Test Street"
        
        # 2. Test status before monitoring starts
        response = self.client.get('/api/status')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert data['data']['monitoring_active'] is False
        
        # 3. Start monitoring
        response = self.client.post('/api/start-monitoring')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        
        # Wait for monitoring to start
        time.sleep(0.5)
        assert self.api.monitoring_service.is_running()
        
        # 4. Test status after monitoring starts
        response = self.client.get('/api/status')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert data['data']['monitoring_active'] is True
        
        # 5. Wait for at least one monitoring cycle
        time.sleep(2.0)
        
        # 6. Check that scraper was called
        assert self.scraper.fetch_listings.called
        
        # 7. Check for property found events
        property_found_events = [e for e in self.received_events if e.event_type == "property_found"]
        assert len(property_found_events) > 0
        
        # 8. Test listings endpoint
        response = self.client.get('/api/listings')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert len(data['data']['listings']) == len(self.test_listings)
        
        # 9. Stop monitoring
        response = self.client.post('/api/stop-monitoring')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        
        # Wait for monitoring to stop
        time.sleep(0.5)
        assert not self.api.monitoring_service.is_running()
    
    def test_error_handling_network_failure(self):
        """Test error handling when network requests fail"""
        # Configure scraper to raise network error
        self.scraper.fetch_listings.side_effect = requests.RequestException("Network error")
        
        # Start monitoring
        response = self.client.post('/api/start-monitoring')
        assert response.status_code == 200
        
        # Wait for monitoring cycle
        time.sleep(2.0)
        
        # Check that error events were generated
        error_events = [e for e in self.received_events if e.event_type == "error"]
        assert len(error_events) > 0
        assert "Network error" in error_events[0].message
        
        # Monitoring should still be running despite errors
        assert self.api.monitoring_service.is_running()
    
    def test_error_handling_parsing_failure(self):
        """Test error handling when HTML parsing fails"""
        # Configure scraper to raise parsing error
        self.scraper.fetch_listings.side_effect = ValueError("Parsing error")
        
        # Start monitoring
        response = self.client.post('/api/start-monitoring')
        assert response.status_code == 200
        
        # Wait for monitoring cycle
        time.sleep(2.0)
        
        # Check that error events were generated
        error_events = [e for e in self.received_events if e.event_type == "error"]
        assert len(error_events) > 0
        assert "Parsing error" in error_events[0].message
    
    def test_configuration_update_during_monitoring(self):
        """Test updating configuration while monitoring is active"""
        # Start monitoring
        response = self.client.post('/api/start-monitoring')
        assert response.status_code == 200
        
        # Wait for monitoring to start
        time.sleep(0.5)
        
        # Update configuration
        new_config = {
            'target_address': '789 New Address',
            'check_interval_minutes': 2,
            'notification_enabled': False
        }
        
        response = self.client.post('/api/config', 
                                  data=json.dumps(new_config),
                                  content_type='application/json')
        assert response.status_code == 200
        
        # Verify configuration was updated
        response = self.client.get('/api/config')
        assert response.status_code == 200
        data = response.get_json()
        assert data['data']['target_address'] == '789 New Address'
        assert data['data']['check_interval_minutes'] == 2
        assert data['data']['notification_enabled'] is False
    
    def test_immediate_check_functionality(self):
        """Test the immediate check functionality"""
        # Test immediate check without monitoring running
        response = self.client.post('/api/check-now')
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert 'listings_count' in data['data']
        
        # Verify scraper was called
        assert self.scraper.fetch_listings.called
    
    def test_property_change_detection(self):
        """Test detection of property changes (found/removed) using immediate checks"""
        # Test using immediate checks instead of waiting for monitoring cycles
        
        # Start with initial listings containing target property
        self.scraper.fetch_listings.return_value = [self.test_listings[0]]
        
        # Perform immediate check
        response = self.client.post('/api/check-now')
        assert response.status_code == 200
        
        # Check that property was found
        found_properties = self.api.monitoring_service.get_found_properties()
        assert "TEST001" in found_properties
        
        # Clear events
        self.received_events.clear()
        
        # Now remove the target property and add a different one
        self.scraper.fetch_listings.return_value = [self.test_listings[1]]  # Only non-target property
        
        # Perform another immediate check
        response = self.client.post('/api/check-now')
        assert response.status_code == 200
        
        # Check that property was removed
        found_properties = self.api.monitoring_service.get_found_properties()
        assert "TEST001" not in found_properties
        
        # Check for property removed events
        property_removed_events = [e for e in self.received_events if e.event_type == "property_removed"]
        assert len(property_removed_events) > 0
        
        # Verify the event contains the correct MLS number
        removed_event = property_removed_events[0]
        assert "TEST001" in removed_event.message
    
    def test_api_error_responses(self):
        """Test API error responses for various failure scenarios"""
        # Test invalid configuration
        invalid_config = {
            'target_address': '',  # Empty address
            'check_interval_minutes': -1  # Invalid interval
        }
        
        response = self.client.post('/api/config',
                                  data=json.dumps(invalid_config),
                                  content_type='application/json')
        assert response.status_code == 400
        data = response.get_json()
        assert data['success'] is False
        assert 'error' in data
        
        # Test starting monitoring without valid configuration
        # First clear the target address by directly modifying the config file
        empty_config = {
            'target_address': '',
            'check_interval_minutes': 15,
            'notification_enabled': True,
            'last_check': None,
            'found_listings': []
        }
        with open(self.config_file, 'w') as f:
            json.dump(empty_config, f)
        
        response = self.client.post('/api/start-monitoring')
        assert response.status_code == 500  # Should fail due to invalid config
        
        # Test stopping monitoring when not running
        response = self.client.post('/api/stop-monitoring')
        assert response.status_code == 400
        data = response.get_json()
        assert data['success'] is False
        assert 'not active' in data['error']
    
    def test_concurrent_api_requests(self):
        """Test handling of concurrent API requests"""
        import concurrent.futures
        
        def make_status_request():
            return self.client.get('/api/status')
        
        def make_config_request():
            return self.client.get('/api/config')
        
        def make_listings_request():
            return self.client.get('/api/listings')
        
        # Make multiple concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for _ in range(10):
                futures.append(executor.submit(make_status_request))
                futures.append(executor.submit(make_config_request))
                futures.append(executor.submit(make_listings_request))
            
            # Wait for all requests to complete
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        for response in results:
            assert response.status_code == 200
            data = response.get_json()
            assert data['success'] is True
    
    def test_graceful_shutdown(self):
        """Test graceful shutdown of monitoring service"""
        # Start monitoring
        response = self.client.post('/api/start-monitoring')
        assert response.status_code == 200
        
        # Wait for monitoring to start
        time.sleep(0.5)
        assert self.api.monitoring_service.is_running()
        
        # Test graceful shutdown
        shutdown_success = self.api.monitoring_service.stop_monitoring(timeout=5.0)
        assert shutdown_success is True
        assert not self.api.monitoring_service.is_running()
    
    def test_notification_integration(self):
        """Test integration between monitoring service and notification system"""
        # Mock the notification manager
        with patch.object(self.api.notification_manager, 'notify_property_found') as mock_notify:
            # Start monitoring
            response = self.client.post('/api/start-monitoring')
            assert response.status_code == 200
            
            # Wait for monitoring cycle
            time.sleep(2.0)
            
            # Check that notification was called for found property
            assert mock_notify.called
            
            # Get the call arguments
            call_args = mock_notify.call_args[0]
            property_listing = call_args[0]
            assert property_listing.address == "123 Test Street"
    
    @patch('requests.Session.get')
    def test_scraper_retry_logic_integration(self, mock_get):
        """Test integration of scraper retry logic with monitoring service"""
        # Configure mock to fail twice then succeed
        mock_response = Mock()
        mock_response.text = '<html><body>Mock listings</body></html>'
        mock_response.raise_for_status.return_value = None
        
        mock_get.side_effect = [
            requests.exceptions.ConnectionError("Connection failed"),
            requests.exceptions.Timeout("Request timed out"),
            mock_response
        ]
        
        # Use real scraper instead of mock for this test
        real_scraper = MeadowNorthScraper()
        self.api.scraper = real_scraper
        self.api.monitoring_service.scraper = real_scraper
        
        # Perform immediate check
        response = self.client.post('/api/check-now')
        
        # Should succeed after retries
        assert response.status_code == 200
        
        # Verify that multiple attempts were made
        assert mock_get.call_count == 3


if __name__ == '__main__':
    pytest.main([__file__, '-v'])