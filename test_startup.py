#!/usr/bin/env python3
"""
Test script for the Real Estate Monitor startup functionality.
This script tests the main application startup without actually running the server.
"""

import sys
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock
import threading
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from main import RealEstateMonitorApp, create_argument_parser, main
        print("✓ Main application imports successful")
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    
    try:
        from backend.app import RealEstateMonitorAPI
        print("✓ Backend API imports successful")
    except ImportError as e:
        print(f"✗ Backend import error: {e}")
        return False
    
    try:
        from shared.config_manager import ConfigManager
        from shared.logging_config import setup_logging
        print("✓ Shared module imports successful")
    except ImportError as e:
        print(f"✗ Shared module import error: {e}")
        return False
    
    return True

def test_argument_parser():
    """Test the command-line argument parser"""
    print("\nTesting argument parser...")
    
    try:
        from main import create_argument_parser
        
        parser = create_argument_parser()
        
        # Test default arguments
        args = parser.parse_args([])
        assert args.host == '127.0.0.1'
        assert args.port == 5000
        assert args.debug == False
        assert args.no_browser == False
        print("✓ Default arguments parsed correctly")
        
        # Test custom arguments
        args = parser.parse_args(['--host', '0.0.0.0', '--port', '8080', '--debug', '--no-browser'])
        assert args.host == '0.0.0.0'
        assert args.port == 8080
        assert args.debug == True
        assert args.no_browser == True
        print("✓ Custom arguments parsed correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Argument parser test failed: {e}")
        return False

def test_app_initialization():
    """Test application initialization"""
    print("\nTesting application initialization...")
    
    try:
        from main import RealEstateMonitorApp
        
        # Test with default parameters
        app = RealEstateMonitorApp()
        assert app.host == '127.0.0.1'
        assert app.port == 5000
        assert app.debug == False
        assert app.no_browser == False
        print("✓ Default app initialization successful")
        
        # Test with custom parameters
        app = RealEstateMonitorApp(host='0.0.0.0', port=8080, debug=True, no_browser=True)
        assert app.host == '0.0.0.0'
        assert app.port == 8080
        assert app.debug == True
        assert app.no_browser == True
        print("✓ Custom app initialization successful")
        
        return True
        
    except Exception as e:
        print(f"✗ App initialization test failed: {e}")
        return False

def test_configuration_check():
    """Test configuration checking functionality"""
    print("\nTesting configuration check...")
    
    try:
        from main import RealEstateMonitorApp
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Temporarily change the data directory
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Use no file logging to avoid Windows file locking issues
                app = RealEstateMonitorApp()
                
                # Patch the logging to avoid file creation
                with patch('shared.logging_config.setup_logging') as mock_logging:
                    mock_logger = MagicMock()
                    mock_logging.return_value = mock_logger
                    app.logger = mock_logger
                    
                    # This should work even with no configuration
                    result = app._check_configuration()
                    assert isinstance(result, bool)
                    print("✓ Configuration check completed")
                
                return True
                
            finally:
                os.chdir(original_cwd)
        
    except Exception as e:
        print(f"✗ Configuration check test failed: {e}")
        return False

def test_signal_handlers():
    """Test signal handler setup"""
    print("\nTesting signal handlers...")
    
    try:
        from main import RealEstateMonitorApp
        import signal
        
        app = RealEstateMonitorApp()
        
        # Check that signal handlers are set up
        # Note: We can't easily test the actual signal handling without triggering it
        print("✓ Signal handlers setup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Signal handler test failed: {e}")
        return False

def test_graceful_shutdown():
    """Test graceful shutdown functionality"""
    print("\nTesting graceful shutdown...")
    
    try:
        from main import RealEstateMonitorApp
        
        app = RealEstateMonitorApp()
        
        # Test shutdown without starting
        app.shutdown()
        print("✓ Graceful shutdown without server successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Graceful shutdown test failed: {e}")
        return False

def test_banner_printing():
    """Test banner and status printing"""
    print("\nTesting banner printing...")
    
    try:
        from main import RealEstateMonitorApp
        from io import StringIO
        import sys
        
        app = RealEstateMonitorApp()
        
        # Capture stdout
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        try:
            app._print_startup_banner()
            app._print_running_status()
            
            output = captured_output.getvalue()
            assert "REAL ESTATE MONITOR" in output
            assert "running" in output.lower()
            print("✓ Banner printing successful")
            
            return True
            
        finally:
            sys.stdout = old_stdout
        
    except Exception as e:
        print(f"✗ Banner printing test failed: {e}")
        return False

def run_all_tests():
    """Run all startup tests"""
    print("="*60)
    print("  REAL ESTATE MONITOR - STARTUP TESTS")
    print("="*60)
    
    tests = [
        test_imports,
        test_argument_parser,
        test_app_initialization,
        test_configuration_check,
        test_signal_handlers,
        test_graceful_shutdown,
        test_banner_printing
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "="*60)
    print(f"  TEST RESULTS: {passed} passed, {failed} failed")
    print("="*60)
    
    if failed == 0:
        print("\n✓ All startup tests passed! The application should start correctly.")
        return True
    else:
        print(f"\n✗ {failed} tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)