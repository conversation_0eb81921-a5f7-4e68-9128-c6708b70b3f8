# Real Estate Monitor - Deployment Guide

This guide provides instructions for setting up and running the Real Estate Monitor application.

## System Requirements

- Python 3.8 or higher
- Internet connection for web scraping
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Operating System: Windows, macOS, or Linux

## Quick Start

### 1. Install Python Dependencies

```bash
# Install required packages
pip install -r requirements.txt
```

### 2. Run the Application

```bash
# Start with default settings
python main.py

# Or with custom options
python main.py --debug --port 8080
```

### 3. Configure Target Address

1. The application will automatically open in your web browser
2. Click the "Configuration" button in the web interface
3. Enter the address you want to monitor
4. Click "Save Configuration"
5. Click "Start Monitoring" to begin automatic checks

## Command Line Options

```bash
python main.py [OPTIONS]

Options:
  --host HOST         Host address to bind server to (default: 127.0.0.1)
  --port PORT         Port number for the server (default: 5000)
  --debug             Enable debug mode with verbose logging
  --no-browser        Don't automatically open web browser
  --version           Show version information
  --help              Show help message
```

## Examples

```bash
# Basic usage
python main.py

# Debug mode with custom port
python main.py --debug --port 8080

# Run without opening browser (useful for servers)
python main.py --no-browser

# Allow external connections (be careful with security)
python main.py --host 0.0.0.0 --port 5000
```

## Configuration

The application stores configuration in JSON files:

- `data/config.json` - Main application configuration
- `data/listings_history.json` - Historical listing data

### Configuration Options

- **Target Address**: The address to monitor on meadownorth.ca
- **Check Interval**: How often to check for new listings (in minutes)
- **Notifications**: Enable/disable desktop and web notifications

## File Structure

```
real-estate-monitor/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── DEPLOYMENT.md          # This deployment guide
├── backend/               # Flask API server
│   ├── app.py            # Main Flask application
│   ├── scraper.py        # Web scraping functionality
│   └── monitoring_service.py # Background monitoring
├── frontend/              # Web interface
│   ├── index.html        # Main HTML page
│   ├── app.js           # JavaScript functionality
│   └── styles.css       # CSS styling
├── shared/               # Shared utilities
│   ├── config_manager.py # Configuration management
│   ├── address_matcher.py # Address matching logic
│   ├── notification_manager.py # Notification system
│   ├── models.py        # Data models
│   └── logging_config.py # Logging configuration
├── data/                 # Application data (created automatically)
│   ├── config.json      # Configuration file
│   └── listings_history.json # Listing history
└── logs/                 # Log files (created automatically)
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Try a different port
   python main.py --port 8080
   ```

2. **Permission Errors**
   ```bash
   # Make sure you have write permissions in the application directory
   # The app needs to create data/ and logs/ directories
   ```

3. **Browser Doesn't Open**
   ```bash
   # Manually open your browser to the displayed URL
   # Or use --no-browser flag and open manually
   ```

4. **Module Not Found Errors**
   ```bash
   # Make sure all dependencies are installed
   pip install -r requirements.txt
   ```

### Debug Mode

Run with `--debug` flag for detailed logging:

```bash
python main.py --debug
```

This will show:
- Detailed HTTP requests and responses
- Web scraping debug information
- Configuration loading details
- Error stack traces

### Log Files

Application logs are stored in the `logs/` directory:
- `real_estate_monitor_YYYYMMDD.log` - Daily log files

### Health Check

Check if the application is running properly:
- Web Interface: `http://localhost:5000`
- API Health: `http://localhost:5000/api/health`

## Security Considerations

- The application runs on localhost (127.0.0.1) by default for security
- Only use `--host 0.0.0.0` if you need external access and understand the risks
- The application does not require any authentication by default
- All data is stored locally on your machine

## Performance

- The application uses minimal system resources
- Web scraping is rate-limited to be respectful to the target website
- Monitoring runs in a background thread and doesn't block the web interface
- Default check interval is 15 minutes (configurable)

## Stopping the Application

- Press `Ctrl+C` in the terminal where the application is running
- The application will gracefully shutdown all services
- All configuration and data will be preserved

## Updates and Maintenance

- Keep Python dependencies updated: `pip install -r requirements.txt --upgrade`
- Check logs regularly for any errors or issues
- The application will handle temporary network issues automatically
- Configuration can be updated through the web interface without restarting

## Support

If you encounter issues:

1. Check the log files in the `logs/` directory
2. Run with `--debug` flag for more detailed information
3. Verify all dependencies are installed correctly
4. Check that the target website (meadownorth.ca) is accessible
5. Ensure you have proper internet connectivity

## Development

For development and testing:

```bash
# Run tests
python -m pytest

# Run with debug mode
python main.py --debug

# Run specific tests
python -m pytest test_integration_e2e.py -v
```