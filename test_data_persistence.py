"""
Tests for the data persistence manager.
"""
import pytest
import json
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from unittest.mock import patch, mock_open

from shared.data_persistence import DataPersistenceManager
from shared.models import PropertyListing


class TestDataPersistenceManager:
    """Test cases for DataPersistenceManager"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def persistence_manager(self, temp_dir):
        """Create a DataPersistenceManager instance for testing"""
        return DataPersistenceManager(temp_dir)
    
    @pytest.fixture
    def sample_listing(self):
        """Create a sample PropertyListing for testing"""
        return PropertyListing(
            address="123 Test Street",
            price="$500,000",
            mls_number="TEST123",
            description="Beautiful test property",
            square_footage="2000",
            bedrooms=3,
            bathrooms=2,
            lot_size="0.25 acres",
            year_built="2020",
            image_url="http://example.com/image.jpg",
            listing_url="http://example.com/listing",
            first_seen=datetime(2024, 1, 15, 10, 30, 0)
        )
    
    def test_init_creates_data_directory(self, temp_dir):
        """Test that initialization creates the data directory"""
        data_dir = Path(temp_dir) / "test_data"
        assert not data_dir.exists()
        
        manager = DataPersistenceManager(str(data_dir))
        assert data_dir.exists()
        assert data_dir.is_dir()
    
    def test_init_with_existing_directory(self, temp_dir):
        """Test initialization with existing directory"""
        manager = DataPersistenceManager(temp_dir)
        assert Path(temp_dir).exists()
    
    def test_init_permission_error(self):
        """Test initialization with permission error"""
        with patch('pathlib.Path.mkdir', side_effect=PermissionError("Access denied")):
            with pytest.raises(IOError, match="Permission denied creating data directory"):
                DataPersistenceManager("/invalid/path")
    
    def test_safe_write_json_success(self, persistence_manager, temp_dir):
        """Test successful JSON writing"""
        test_file = Path(temp_dir) / "test.json"
        test_data = {"key": "value", "number": 42}
        
        persistence_manager._safe_write_json(test_file, test_data)
        
        assert test_file.exists()
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        assert loaded_data == test_data
    
    def test_safe_write_json_with_backup(self, persistence_manager, temp_dir):
        """Test JSON writing with backup creation and cleanup"""
        test_file = Path(temp_dir) / "test.json"
        backup_file = test_file.with_suffix(test_file.suffix + ".backup")
        
        # Create initial file
        initial_data = {"version": 1}
        with open(test_file, 'w') as f:
            json.dump(initial_data, f)
        
        # Update file
        new_data = {"version": 2}
        persistence_manager._safe_write_json(test_file, new_data)
        
        # Verify new data is written
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        assert loaded_data == new_data
        
        # Verify backup was cleaned up
        assert not backup_file.exists()
    
    def test_safe_write_json_restore_on_failure(self, persistence_manager, temp_dir):
        """Test backup restoration on write failure"""
        test_file = Path(temp_dir) / "test.json"
        
        # Create initial file
        initial_data = {"version": 1}
        with open(test_file, 'w') as f:
            json.dump(initial_data, f)
        
        # Mock json.dump to fail
        with patch('json.dump', side_effect=Exception("Write failed")):
            with pytest.raises(IOError, match="Failed to write"):
                persistence_manager._safe_write_json(test_file, {"version": 2})
        
        # Verify original file is restored
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        assert loaded_data == initial_data
    
    def test_safe_read_json_success(self, persistence_manager, temp_dir):
        """Test successful JSON reading"""
        test_file = Path(temp_dir) / "test.json"
        test_data = {"key": "value", "number": 42}
        
        with open(test_file, 'w') as f:
            json.dump(test_data, f)
        
        loaded_data = persistence_manager._safe_read_json(test_file)
        assert loaded_data == test_data
    
    def test_safe_read_json_file_not_found(self, persistence_manager, temp_dir):
        """Test reading non-existent file with default"""
        test_file = Path(temp_dir) / "nonexistent.json"
        default_data = {"default": True}
        
        loaded_data = persistence_manager._safe_read_json(test_file, default_data)
        assert loaded_data == default_data
    
    def test_safe_read_json_file_not_found_no_default(self, persistence_manager, temp_dir):
        """Test reading non-existent file without default"""
        test_file = Path(temp_dir) / "nonexistent.json"
        
        with pytest.raises(FileNotFoundError):
            persistence_manager._safe_read_json(test_file)
    
    def test_safe_read_json_corrupted_file(self, persistence_manager, temp_dir):
        """Test reading corrupted JSON file with default"""
        test_file = Path(temp_dir) / "corrupted.json"
        default_data = {"default": True}
        
        # Create corrupted JSON file
        with open(test_file, 'w') as f:
            f.write("{ invalid json")
        
        loaded_data = persistence_manager._safe_read_json(test_file, default_data)
        assert loaded_data == default_data
    
    def test_safe_read_json_corrupted_file_no_default(self, persistence_manager, temp_dir):
        """Test reading corrupted JSON file without default"""
        test_file = Path(temp_dir) / "corrupted.json"
        
        # Create corrupted JSON file
        with open(test_file, 'w') as f:
            f.write("{ invalid json")
        
        with pytest.raises(IOError, match="corrupted"):
            persistence_manager._safe_read_json(test_file)
    
    def test_safe_read_json_restore_from_backup(self, persistence_manager, temp_dir):
        """Test restoration from backup when main file is corrupted"""
        test_file = Path(temp_dir) / "test.json"
        backup_file = test_file.with_suffix(test_file.suffix + ".backup")
        backup_data = {"restored": True}
        
        # Create backup file
        with open(backup_file, 'w') as f:
            json.dump(backup_data, f)
        
        # Create corrupted main file
        with open(test_file, 'w') as f:
            f.write("{ invalid json")
        
        loaded_data = persistence_manager._safe_read_json(test_file, {"default": True})
        assert loaded_data == backup_data
    
    def test_save_listings_history(self, persistence_manager, sample_listing):
        """Test saving listings history"""
        listings = [sample_listing]
        
        persistence_manager.save_listings_history(listings)
        
        # Verify file was created
        assert persistence_manager.listings_history_file.exists()
        
        # Verify content
        with open(persistence_manager.listings_history_file, 'r') as f:
            data = json.load(f)
        
        assert data["schema_version"] == persistence_manager.CURRENT_SCHEMA_VERSION
        assert len(data["listings"]) == 1
        assert data["listings"][0]["address"] == "123 Test Street"
        assert data["listings"][0]["mls_number"] == "TEST123"
    
    def test_load_listings_history(self, persistence_manager, sample_listing):
        """Test loading listings history"""
        # Save first
        listings = [sample_listing]
        persistence_manager.save_listings_history(listings)
        
        # Load and verify
        loaded_listings = persistence_manager.load_listings_history()
        
        assert len(loaded_listings) == 1
        assert loaded_listings[0].address == "123 Test Street"
        assert loaded_listings[0].mls_number == "TEST123"
        assert isinstance(loaded_listings[0].first_seen, datetime)
    
    def test_load_listings_history_empty(self, persistence_manager):
        """Test loading listings history when file doesn't exist"""
        loaded_listings = persistence_manager.load_listings_history()
        assert loaded_listings == []
    
    def test_append_to_listings_history(self, persistence_manager, sample_listing):
        """Test appending to listings history"""
        # Create initial listing
        initial_listing = PropertyListing(
            address="456 Initial Street",
            price="$400,000",
            mls_number="INIT456",
            description="Initial property"
        )
        persistence_manager.save_listings_history([initial_listing])
        
        # Append new listing
        persistence_manager.append_to_listings_history(sample_listing)
        
        # Verify both listings exist
        loaded_listings = persistence_manager.load_listings_history()
        assert len(loaded_listings) == 2
        
        mls_numbers = {listing.mls_number for listing in loaded_listings}
        assert "INIT456" in mls_numbers
        assert "TEST123" in mls_numbers
    
    def test_append_to_listings_history_duplicate(self, persistence_manager, sample_listing):
        """Test appending duplicate listing (should not duplicate)"""
        # Save initial listing
        persistence_manager.save_listings_history([sample_listing])
        
        # Try to append same listing
        persistence_manager.append_to_listings_history(sample_listing)
        
        # Verify only one listing exists
        loaded_listings = persistence_manager.load_listings_history()
        assert len(loaded_listings) == 1
        assert loaded_listings[0].mls_number == "TEST123"
    
    def test_get_listings_by_date_range(self, persistence_manager):
        """Test getting listings by date range"""
        # Create listings with different dates
        listing1 = PropertyListing(
            address="123 Old Street",
            price="$300,000",
            mls_number="OLD123",
            description="Old property",
            first_seen=datetime(2024, 1, 1, 10, 0, 0)
        )
        
        listing2 = PropertyListing(
            address="456 New Street",
            price="$400,000",
            mls_number="NEW456",
            description="New property",
            first_seen=datetime(2024, 2, 1, 10, 0, 0)
        )
        
        persistence_manager.save_listings_history([listing1, listing2])
        
        # Get listings in January 2024
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31, 23, 59, 59)
        
        filtered_listings = persistence_manager.get_listings_by_date_range(start_date, end_date)
        
        assert len(filtered_listings) == 1
        assert filtered_listings[0].mls_number == "OLD123"
    
    def test_save_monitoring_state(self, persistence_manager):
        """Test saving monitoring state"""
        state = {
            "is_running": True,
            "current_status": "active",
            "last_check_time": "2024-01-15T10:30:00",
            "found_properties": ["TEST123", "TEST456"]
        }
        
        persistence_manager.save_monitoring_state(state)
        
        # Verify file was created
        assert persistence_manager.monitoring_state_file.exists()
        
        # Verify content
        with open(persistence_manager.monitoring_state_file, 'r') as f:
            data = json.load(f)
        
        assert data["schema_version"] == persistence_manager.CURRENT_SCHEMA_VERSION
        assert data["state"] == state
    
    def test_load_monitoring_state(self, persistence_manager):
        """Test loading monitoring state"""
        state = {
            "is_running": False,
            "current_status": "stopped",
            "last_check_time": "2024-01-15T10:30:00",
            "found_properties": ["TEST123"]
        }
        
        # Save first
        persistence_manager.save_monitoring_state(state)
        
        # Load and verify
        loaded_state = persistence_manager.load_monitoring_state()
        assert loaded_state == state
    
    def test_load_monitoring_state_empty(self, persistence_manager):
        """Test loading monitoring state when file doesn't exist"""
        loaded_state = persistence_manager.load_monitoring_state()
        assert loaded_state == {}
    
    def test_migrate_config_no_migration_needed(self, persistence_manager):
        """Test config migration when no migration is needed"""
        config_data = {
            "schema_version": "1.0",
            "target_address": "123 Test Street",
            "check_interval_minutes": 15
        }
        
        migrated = persistence_manager.migrate_config(config_data)
        assert migrated == config_data
    
    def test_migrate_config_from_v0(self, persistence_manager):
        """Test config migration from version 0.0"""
        config_data = {
            "target_address": "123 Test Street",
            "check_interval_minutes": 10
        }
        
        migrated = persistence_manager.migrate_config(config_data)
        
        assert migrated["schema_version"] == "1.0"
        assert migrated["target_address"] == "123 Test Street"
        assert migrated["check_interval_minutes"] == 10
        assert migrated["notification_enabled"] == True
        assert migrated["last_check"] is None
        assert migrated["found_listings"] == []
    
    def test_migrate_listings_history(self, persistence_manager, temp_dir):
        """Test listings history migration"""
        # Create old format history file
        old_history = {
            "listings": [
                {
                    "address": "123 Test Street",
                    "price": "$500,000",
                    "mls_number": "TEST123",
                    "description": "Test property"
                    # Missing optional fields
                }
            ]
        }
        
        with open(persistence_manager.listings_history_file, 'w') as f:
            json.dump(old_history, f)
        
        # Load (should trigger migration)
        loaded_listings = persistence_manager.load_listings_history()
        
        assert len(loaded_listings) == 1
        listing = loaded_listings[0]
        assert listing.address == "123 Test Street"
        assert listing.square_footage is None
        assert listing.bedrooms is None
        assert listing.image_url == ""
        assert isinstance(listing.first_seen, datetime)
    
    def test_migrate_monitoring_state(self, persistence_manager, temp_dir):
        """Test monitoring state migration"""
        # Create old format state file
        old_state = {
            "state": {
                "some_old_field": "value"
            }
        }
        
        with open(persistence_manager.monitoring_state_file, 'w') as f:
            json.dump(old_state, f)
        
        # Load (should trigger migration)
        loaded_state = persistence_manager.load_monitoring_state()
        
        assert loaded_state["is_running"] == False
        assert loaded_state["current_status"] == "stopped"
        assert loaded_state["last_check_time"] is None
        assert loaded_state["found_properties"] == []
    
    def test_cleanup_old_backups(self, persistence_manager, temp_dir):
        """Test cleanup of old backup files"""
        # Create multiple backup files with different timestamps
        import time
        
        backup_files = []
        for i in range(7):  # Create 7 backup files
            backup_file = Path(temp_dir) / f"test.json.backup.{i}"
            backup_file.touch()
            backup_files.append(backup_file)
            time.sleep(0.01)  # Ensure different timestamps
        
        # Run cleanup (keep max 5)
        persistence_manager.cleanup_old_backups(max_backups=5)
        
        # Count remaining backup files
        remaining_backups = list(Path(temp_dir).glob("test.json.backup*"))
        assert len(remaining_backups) <= 5
    
    def test_get_data_directory_info(self, persistence_manager, sample_listing):
        """Test getting data directory information"""
        # Create some files
        persistence_manager.save_listings_history([sample_listing])
        persistence_manager.save_monitoring_state({"test": True})
        
        info = persistence_manager.get_data_directory_info()
        
        assert info["data_directory"] == str(persistence_manager.data_dir)
        assert info["exists"] == True
        assert "files" in info
        
        files_info = info["files"]
        assert "listings_history.json" in files_info
        assert "monitoring_state.json" in files_info
        
        # Check file info structure
        history_info = files_info["listings_history.json"]
        assert history_info["exists"] == True
        assert history_info["size"] > 0
        assert history_info["modified"] is not None
    
    def test_permission_error_handling(self, persistence_manager, temp_dir):
        """Test handling of permission errors"""
        test_file = Path(temp_dir) / "readonly.json"
        
        # Create file and make it read-only
        test_file.touch()
        
        # On Windows, use different approach for read-only
        import os
        import stat
        
        try:
            # Make file read-only
            os.chmod(test_file, stat.S_IREAD)
            
            with pytest.raises(IOError, match="Failed to write"):
                persistence_manager._safe_write_json(test_file, {"test": "data"})
        finally:
            # Restore write permissions for cleanup
            try:
                os.chmod(test_file, stat.S_IWRITE | stat.S_IREAD)
                # Also fix any backup files that might have been created
                backup_file = test_file.with_suffix(test_file.suffix + ".backup")
                if backup_file.exists():
                    os.chmod(backup_file, stat.S_IWRITE | stat.S_IREAD)
            except:
                pass
    
    def test_invalid_listing_data_handling(self, persistence_manager, temp_dir):
        """Test handling of invalid listing data in history"""
        # Create history with invalid listing data
        invalid_history = {
            "schema_version": "1.0",
            "listings": [
                {
                    "address": "123 Valid Street",
                    "price": "$500,000",
                    "mls_number": "VALID123",
                    "description": "Valid property"
                },
                {
                    # Missing required fields
                    "address": "",
                    "price": "$400,000"
                    # Missing mls_number and description
                }
            ]
        }
        
        with open(persistence_manager.listings_history_file, 'w') as f:
            json.dump(invalid_history, f)
        
        # Load should skip invalid listings
        loaded_listings = persistence_manager.load_listings_history()
        
        assert len(loaded_listings) == 1
        assert loaded_listings[0].mls_number == "VALID123"


if __name__ == "__main__":
    pytest.main([__file__])