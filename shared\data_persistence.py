"""
Data persistence manager for the Real Estate Monitor application.
Handles JSON file storage, state management, and data migration.
"""
import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import asdict
import logging

from .models import PropertyListing, Config


class DataPersistenceManager:
    """
    Manages all data persistence operations including configuration,
    listing history, monitoring state, and data migration.
    """
    
    # File names
    CONFIG_FILE = "config.json"
    LISTINGS_HISTORY_FILE = "listings_history.json"
    MONITORING_STATE_FILE = "monitoring_state.json"
    BACKUP_SUFFIX = ".backup"
    
    # Current data schema version
    CURRENT_SCHEMA_VERSION = "1.0"
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the data persistence manager.
        
        Args:
            data_dir: Directory to store data files
        """
        self.data_dir = Path(data_dir)
        self.logger = logging.getLogger(__name__)
        
        # Ensure data directory exists
        self._ensure_data_directory()
        
        # File paths
        self.config_file = self.data_dir / self.CONFIG_FILE
        self.listings_history_file = self.data_dir / self.LISTINGS_HISTORY_FILE
        self.monitoring_state_file = self.data_dir / self.MONITORING_STATE_FILE
    
    def _ensure_data_directory(self):
        """Ensure the data directory exists with proper permissions"""
        try:
            self.data_dir.mkdir(exist_ok=True, mode=0o755)
        except PermissionError as e:
            raise IOError(f"Permission denied creating data directory {self.data_dir}: {e}")
        except Exception as e:
            raise IOError(f"Error creating data directory {self.data_dir}: {e}")
    
    def _create_backup(self, file_path: Path) -> Optional[Path]:
        """
        Create a backup of a file before modifying it.
        
        Args:
            file_path: Path to the file to backup
            
        Returns:
            Path to backup file if created, None if original doesn't exist
        """
        if not file_path.exists():
            return None
        
        backup_path = file_path.with_suffix(file_path.suffix + self.BACKUP_SUFFIX)
        try:
            shutil.copy2(file_path, backup_path)
            return backup_path
        except Exception as e:
            self.logger.warning(f"Could not create backup of {file_path}: {e}")
            return None
    
    def _restore_from_backup(self, file_path: Path) -> bool:
        """
        Restore a file from its backup.
        
        Args:
            file_path: Path to the file to restore
            
        Returns:
            True if restored successfully, False otherwise
        """
        backup_path = file_path.with_suffix(file_path.suffix + self.BACKUP_SUFFIX)
        if not backup_path.exists():
            return False
        
        try:
            shutil.copy2(backup_path, file_path)
            return True
        except Exception as e:
            self.logger.error(f"Could not restore from backup {backup_path}: {e}")
            return False
    
    def _safe_write_json(self, file_path: Path, data: Dict[str, Any]) -> None:
        """
        Safely write JSON data to a file with backup and corruption protection.
        
        Args:
            file_path: Path to write to
            data: Data to write
            
        Raises:
            IOError: If write operation fails
        """
        # Create backup if file exists
        backup_path = self._create_backup(file_path)
        
        try:
            # Write to temporary file first
            temp_path = file_path.with_suffix(file_path.suffix + ".tmp")
            
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            
            # Verify the written file is valid JSON
            with open(temp_path, 'r', encoding='utf-8') as f:
                json.load(f)
            
            # Atomically replace the original file
            if os.name == 'nt':  # Windows
                if file_path.exists():
                    file_path.unlink()
            temp_path.replace(file_path)
            
            # Clean up backup on successful write
            if backup_path and backup_path.exists():
                backup_path.unlink()
                
        except Exception as e:
            # Restore from backup if available
            if backup_path and self._restore_from_backup(file_path):
                self.logger.warning(f"Restored {file_path} from backup after write failure")
            
            # Clean up temporary file
            temp_path = file_path.with_suffix(file_path.suffix + ".tmp")
            if temp_path.exists():
                try:
                    temp_path.unlink()
                except:
                    pass
            
            raise IOError(f"Failed to write {file_path}: {e}")
    
    def _safe_read_json(self, file_path: Path, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Safely read JSON data from a file with corruption handling.
        
        Args:
            file_path: Path to read from
            default: Default value if file doesn't exist or is corrupted
            
        Returns:
            Loaded JSON data or default value
            
        Raises:
            IOError: If file cannot be read and no backup is available
        """
        if not file_path.exists():
            if default is not None:
                return default
            raise FileNotFoundError(f"File not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            self.logger.warning(f"Corrupted file {file_path}: {e}")
            
            # Try to restore from backup
            if self._restore_from_backup(file_path):
                self.logger.info(f"Restored {file_path} from backup")
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception:
                    pass
            
            # If backup restoration fails or backup is also corrupted
            if default is not None:
                self.logger.warning(f"Using default data for corrupted file {file_path}")
                return default
            
            raise IOError(f"File {file_path} is corrupted and cannot be restored")
        
        except PermissionError as e:
            raise IOError(f"Permission denied reading {file_path}: {e}")
        except Exception as e:
            raise IOError(f"Error reading {file_path}: {e}")
    
    # Listing History Management
    
    def save_listings_history(self, listings: List[PropertyListing]) -> None:
        """
        Save property listings history to file.
        
        Args:
            listings: List of PropertyListing objects to save
        """
        try:
            # Load existing history
            history_data = self._safe_read_json(self.listings_history_file, {
                "schema_version": self.CURRENT_SCHEMA_VERSION,
                "last_updated": None,
                "listings": []
            })
            
            # Convert listings to dictionaries
            listings_data = []
            for listing in listings:
                listing_dict = asdict(listing)
                # Convert datetime to ISO string
                if isinstance(listing_dict.get('first_seen'), datetime):
                    listing_dict['first_seen'] = listing_dict['first_seen'].isoformat()
                listings_data.append(listing_dict)
            
            # Update history data
            history_data.update({
                "last_updated": datetime.now().isoformat(),
                "listings": listings_data
            })
            
            # Save to file
            self._safe_write_json(self.listings_history_file, history_data)
            self.logger.debug(f"Saved {len(listings)} listings to history")
            
        except Exception as e:
            self.logger.error(f"Failed to save listings history: {e}")
            raise
    
    def load_listings_history(self) -> List[PropertyListing]:
        """
        Load property listings history from file.
        
        Returns:
            List of PropertyListing objects
        """
        try:
            history_data = self._safe_read_json(self.listings_history_file, {
                "schema_version": self.CURRENT_SCHEMA_VERSION,
                "listings": []
            })
            
            # Check for schema migration needs
            self._migrate_listings_history(history_data)
            
            # Convert dictionaries back to PropertyListing objects
            listings = []
            for listing_dict in history_data.get("listings", []):
                # Convert ISO string back to datetime
                if isinstance(listing_dict.get('first_seen'), str):
                    try:
                        listing_dict['first_seen'] = datetime.fromisoformat(listing_dict['first_seen'])
                    except ValueError:
                        listing_dict['first_seen'] = datetime.now()
                
                # Handle missing fields with defaults
                listing_dict.setdefault('square_footage', None)
                listing_dict.setdefault('bedrooms', None)
                listing_dict.setdefault('bathrooms', None)
                listing_dict.setdefault('lot_size', None)
                listing_dict.setdefault('year_built', None)
                listing_dict.setdefault('image_url', "")
                listing_dict.setdefault('listing_url', "")
                listing_dict.setdefault('first_seen', datetime.now())
                
                try:
                    listings.append(PropertyListing(**listing_dict))
                except Exception as e:
                    self.logger.warning(f"Skipping invalid listing in history: {e}")
                    continue
            
            self.logger.debug(f"Loaded {len(listings)} listings from history")
            return listings
            
        except FileNotFoundError:
            self.logger.debug("No listings history file found, returning empty list")
            return []
        except Exception as e:
            self.logger.error(f"Failed to load listings history: {e}")
            return []
    
    def append_to_listings_history(self, listing: PropertyListing) -> None:
        """
        Append a single listing to the history file.
        
        Args:
            listing: PropertyListing to append
        """
        try:
            # Load existing listings
            existing_listings = self.load_listings_history()
            
            # Check if listing already exists (by MLS number)
            existing_mls = {l.mls_number for l in existing_listings}
            if listing.mls_number not in existing_mls:
                existing_listings.append(listing)
                self.save_listings_history(existing_listings)
                self.logger.debug(f"Appended listing {listing.mls_number} to history")
            
        except Exception as e:
            self.logger.error(f"Failed to append listing to history: {e}")
            raise
    
    def get_listings_by_date_range(self, start_date: datetime, end_date: datetime) -> List[PropertyListing]:
        """
        Get listings within a specific date range.
        
        Args:
            start_date: Start of date range
            end_date: End of date range
            
        Returns:
            List of PropertyListing objects within the date range
        """
        try:
            all_listings = self.load_listings_history()
            filtered_listings = [
                listing for listing in all_listings
                if start_date <= listing.first_seen <= end_date
            ]
            return filtered_listings
        except Exception as e:
            self.logger.error(f"Failed to get listings by date range: {e}")
            return []
    
    # Monitoring State Management
    
    def save_monitoring_state(self, state: Dict[str, Any]) -> None:
        """
        Save monitoring service state to file.
        
        Args:
            state: Dictionary containing monitoring state
        """
        try:
            state_data = {
                "schema_version": self.CURRENT_SCHEMA_VERSION,
                "last_updated": datetime.now().isoformat(),
                "state": state
            }
            
            self._safe_write_json(self.monitoring_state_file, state_data)
            self.logger.debug("Saved monitoring state")
            
        except Exception as e:
            self.logger.error(f"Failed to save monitoring state: {e}")
            raise
    
    def load_monitoring_state(self) -> Dict[str, Any]:
        """
        Load monitoring service state from file.
        
        Returns:
            Dictionary containing monitoring state
        """
        try:
            state_data = self._safe_read_json(self.monitoring_state_file, {
                "schema_version": self.CURRENT_SCHEMA_VERSION,
                "state": {}
            })
            
            # Check for schema migration needs
            self._migrate_monitoring_state(state_data)
            
            return state_data.get("state", {})
            
        except FileNotFoundError:
            self.logger.debug("No monitoring state file found, returning empty state")
            return {}
        except Exception as e:
            self.logger.error(f"Failed to load monitoring state: {e}")
            return {}
    
    # Data Migration
    
    def _migrate_listings_history(self, history_data: Dict[str, Any]) -> None:
        """
        Migrate listings history data to current schema version.
        
        Args:
            history_data: History data dictionary to migrate
        """
        current_version = history_data.get("schema_version", "0.0")
        
        if current_version == self.CURRENT_SCHEMA_VERSION:
            return
        
        self.logger.info(f"Migrating listings history from version {current_version} to {self.CURRENT_SCHEMA_VERSION}")
        
        # Migration logic for different versions
        if current_version == "0.0":
            # Add schema version if missing
            history_data["schema_version"] = "1.0"
            
            # Ensure all listings have required fields
            for listing in history_data.get("listings", []):
                listing.setdefault("square_footage", None)
                listing.setdefault("bedrooms", None)
                listing.setdefault("bathrooms", None)
                listing.setdefault("lot_size", None)
                listing.setdefault("year_built", None)
                listing.setdefault("image_url", "")
                listing.setdefault("listing_url", "")
                
                # Ensure first_seen is present
                if "first_seen" not in listing:
                    listing["first_seen"] = datetime.now().isoformat()
        
        # Save migrated data
        try:
            self._safe_write_json(self.listings_history_file, history_data)
            self.logger.info("Listings history migration completed successfully")
        except Exception as e:
            self.logger.error(f"Failed to save migrated listings history: {e}")
            raise
    
    def _migrate_monitoring_state(self, state_data: Dict[str, Any]) -> None:
        """
        Migrate monitoring state data to current schema version.
        
        Args:
            state_data: State data dictionary to migrate
        """
        current_version = state_data.get("schema_version", "0.0")
        
        if current_version == self.CURRENT_SCHEMA_VERSION:
            return
        
        self.logger.info(f"Migrating monitoring state from version {current_version} to {self.CURRENT_SCHEMA_VERSION}")
        
        # Migration logic for different versions
        if current_version == "0.0":
            # Add schema version if missing
            state_data["schema_version"] = "1.0"
            
            # Ensure state has required fields
            state = state_data.setdefault("state", {})
            state.setdefault("is_running", False)
            state.setdefault("current_status", "stopped")
            state.setdefault("last_check_time", None)
            state.setdefault("found_properties", [])
        
        # Save migrated data
        try:
            self._safe_write_json(self.monitoring_state_file, state_data)
            self.logger.info("Monitoring state migration completed successfully")
        except Exception as e:
            self.logger.error(f"Failed to save migrated monitoring state: {e}")
            raise
    
    def migrate_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate configuration data to current schema version.
        
        Args:
            config_data: Configuration data dictionary to migrate
            
        Returns:
            Migrated configuration data
        """
        current_version = config_data.get("schema_version", "0.0")
        
        if current_version == self.CURRENT_SCHEMA_VERSION:
            return config_data
        
        self.logger.info(f"Migrating configuration from version {current_version} to {self.CURRENT_SCHEMA_VERSION}")
        
        # Create a copy to avoid modifying the original
        migrated_config = config_data.copy()
        
        # Migration logic for different versions
        if current_version == "0.0":
            # Add schema version if missing
            migrated_config["schema_version"] = "1.0"
            
            # Ensure all required fields are present
            migrated_config.setdefault("target_address", "1 PLACEHOLDER STREET")
            migrated_config.setdefault("check_interval_minutes", 15)
            migrated_config.setdefault("notification_enabled", True)
            migrated_config.setdefault("last_check", None)
            migrated_config.setdefault("found_listings", [])
        
        return migrated_config
    
    # Utility Methods
    
    def cleanup_old_backups(self, max_backups: int = 5) -> None:
        """
        Clean up old backup files, keeping only the most recent ones.
        
        Args:
            max_backups: Maximum number of backup files to keep per file type
        """
        try:
            backup_files = list(self.data_dir.glob(f"*{self.BACKUP_SUFFIX}*"))
            
            # Group backups by base filename
            backup_groups = {}
            for backup_file in backup_files:
                # Extract base name by removing backup suffix and any additional extensions
                name_parts = backup_file.name.split(self.BACKUP_SUFFIX)
                if len(name_parts) >= 2:
                    base_name = name_parts[0]
                    if base_name not in backup_groups:
                        backup_groups[base_name] = []
                    backup_groups[base_name].append(backup_file)
            
            # Clean up each group
            for base_name, backups in backup_groups.items():
                if len(backups) > max_backups:
                    # Sort by modification time (oldest first)
                    backups.sort(key=lambda f: f.stat().st_mtime)
                    
                    # Remove oldest backups
                    for backup_file in backups[:-max_backups]:
                        try:
                            backup_file.unlink()
                            self.logger.debug(f"Removed old backup: {backup_file}")
                        except Exception as e:
                            self.logger.warning(f"Could not remove backup {backup_file}: {e}")
            
        except Exception as e:
            self.logger.warning(f"Error during backup cleanup: {e}")
    
    def get_data_directory_info(self) -> Dict[str, Any]:
        """
        Get information about the data directory and files.
        
        Returns:
            Dictionary with data directory information
        """
        try:
            info = {
                "data_directory": str(self.data_dir),
                "exists": self.data_dir.exists(),
                "files": {}
            }
            
            if self.data_dir.exists():
                for file_path in [self.config_file, self.listings_history_file, self.monitoring_state_file]:
                    file_info = {
                        "exists": file_path.exists(),
                        "size": file_path.stat().st_size if file_path.exists() else 0,
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat() if file_path.exists() else None
                    }
                    info["files"][file_path.name] = file_info
            
            return info
            
        except Exception as e:
            self.logger.error(f"Error getting data directory info: {e}")
            return {"error": str(e)}