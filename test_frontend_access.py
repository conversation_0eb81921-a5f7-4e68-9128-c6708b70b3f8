#!/usr/bin/env python3
"""
Test script to verify frontend access works correctly.
"""

import sys
import os
import threading
import time
import requests

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_frontend_access():
    """Test that the frontend can be accessed through the Flask app"""
    print("Testing frontend access...")
    
    try:
        from main import RealEstateMonitorApp
        
        # Use a test port
        test_port = 5005
        app = RealEstateMonitorApp(host='127.0.0.1', port=test_port, debug=False, no_browser=True)
        
        print(f"Starting application on port {test_port}...")
        
        # Start the application in a separate thread
        app_thread = threading.Thread(target=app.start, daemon=True)
        app_thread.start()
        
        # Wait for the server to start
        print("Waiting for server to start...")
        max_attempts = 15
        server_started = False
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"http://127.0.0.1:{test_port}/api/health", timeout=2)
                if response.status_code in [200, 503]:
                    server_started = True
                    print(f"✓ Server started successfully (attempt {attempt + 1})")
                    break
            except requests.exceptions.RequestException:
                if attempt < max_attempts - 1:
                    time.sleep(1)
                    continue
        
        if not server_started:
            print("✗ Server failed to start within timeout")
            return False
        
        # Test frontend access
        print("Testing frontend access...")
        
        try:
            response = requests.get(f"http://127.0.0.1:{test_port}/", timeout=5)
            if response.status_code == 200:
                print("✓ Frontend HTML page loads successfully")
                
                # Check if it contains expected content
                if "Real Estate Monitor" in response.text:
                    print("✓ Frontend contains expected content")
                else:
                    print("✗ Frontend missing expected content")
                    return False
            else:
                print(f"✗ Frontend returned status {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Frontend access failed: {e}")
            return False
        
        # Test static file access
        print("Testing static file access...")
        
        try:
            css_response = requests.get(f"http://127.0.0.1:{test_port}/static/styles.css", timeout=5)
            if css_response.status_code == 200:
                print("✓ CSS file loads successfully")
            else:
                print(f"✗ CSS file returned status {css_response.status_code}")
        except Exception as e:
            print(f"✗ CSS file access failed: {e}")
        
        try:
            js_response = requests.get(f"http://127.0.0.1:{test_port}/static/app.js", timeout=5)
            if js_response.status_code == 200:
                print("✓ JavaScript file loads successfully")
            else:
                print(f"✗ JavaScript file returned status {js_response.status_code}")
        except Exception as e:
            print(f"✗ JavaScript file access failed: {e}")
        
        # Test API endpoints
        print("Testing API endpoints...")
        
        try:
            config_response = requests.get(f"http://127.0.0.1:{test_port}/api/config", timeout=5)
            if config_response.status_code == 200:
                print("✓ API config endpoint works")
            else:
                print(f"✗ API config endpoint returned status {config_response.status_code}")
        except Exception as e:
            print(f"✗ API config endpoint failed: {e}")
        
        # Shutdown
        print("Shutting down...")
        app.shutdown()
        time.sleep(2)
        
        print("\n✓ Frontend access test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Frontend access test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    if test_frontend_access():
        print("\n✓ All frontend tests passed!")
        print("\nTo access the application:")
        print("1. Run: python main.py")
        print("2. Open your browser to: http://127.0.0.1:5000")
        print("3. Click 'Settings' to configure your target address")
        sys.exit(0)
    else:
        print("\n✗ Frontend tests failed!")
        sys.exit(1)