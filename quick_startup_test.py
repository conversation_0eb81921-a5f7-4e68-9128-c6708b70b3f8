#!/usr/bin/env python3
"""
Quick startup test to verify the application starts correctly.
"""

import sys
import os
import subprocess
import time
import requests
import threading

def test_quick_startup():
    """Test that the application starts and responds to requests"""
    print("Testing quick application startup...")
    
    try:
        # Start the application in a subprocess
        process = subprocess.Popen([
            sys.executable, 'main.py', 
            '--no-browser', '--port', '5003'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for startup
        time.sleep(5)
        
        # Test if server is responding
        try:
            response = requests.get('http://127.0.0.1:5003/api/health', timeout=5)
            if response.status_code in [200, 503]:
                print("✓ Application started and is responding to requests")
                success = True
            else:
                print(f"✗ Application responded with unexpected status: {response.status_code}")
                success = False
        except requests.exceptions.RequestException as e:
            print(f"✗ Application not responding: {e}")
            success = False
        
        # Terminate the process
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        return success
        
    except Exception as e:
        print(f"✗ Startup test failed: {e}")
        return False

if __name__ == '__main__':
    if test_quick_startup():
        print("\n✓ Quick startup test passed!")
        sys.exit(0)
    else:
        print("\n✗ Quick startup test failed!")
        sys.exit(1)