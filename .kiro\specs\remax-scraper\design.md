# Design Document

## Overview

This design integrates RE/MAX Canada website scraping into the existing real estate monitoring application. The current system monitors meadownorth.ca through a single scraper, and this enhancement adds RE/MAX as an additional data source. The integration follows the existing architecture patterns, creating a new scraper class that implements the same interface as the current MeadowNorthScraper, and modifying the monitoring service to aggregate results from multiple sources.

## Architecture

The enhanced architecture maintains the existing modular design while adding multi-source scraping capability:

```mermaid
graph TD
    A[Web Frontend] --> B[Flask Backend]
    B --> C[Configuration Manager]
    B --> D[Monitoring Service]
    B --> E[Address Matcher]
    B --> F[Notification System]
    B --> G[Data Storage]
    
    D --> H[MeadowNorth Scraper]
    D --> I[RE/MAX Scraper]
    
    H --> J[HTTP Client]
    H --> K[HTML Parser]
    I --> L[HTTP Client]
    I --> M[HTML Parser]
    I --> N[Pagination Handler]
    
    E --> O[Fuzzy Matching]
    F --> P[Web Notifications]
    F --> Q[Desktop Notifications]
    G --> R[JSON File Storage]
```

### Key Changes:

1. **New RE/MAX Scraper**: Separate scraper class for RE/MAX website
2. **Enhanced Monitoring Service**: Modified to handle multiple scrapers
3. **Aggregated Results**: Combined listings from all sources
4. **Source Attribution**: Listings tagged with their source for display

## Components and Interfaces

### RE/MAX Scraper Class
```python
class RemaxScraper:
    def __init__(self, base_url: str = "https://www.remax.ca")
    def fetch_listings(self) -> List[PropertyListing]
    def parse_listing_html(self, html: str) -> List[PropertyListing]
    def extract_property_data(self, element) -> PropertyListing
    def _handle_pagination(self) -> List[str]
    def _extract_price(self, element) -> Optional[str]
    def _extract_address(self, element) -> Optional[str]
    def _extract_mls_number(self, element) -> Optional[str]
    # ... other extraction methods
```

**Responsibilities:**
- HTTP requests to remax.ca/sk/meadow-lake-real-estate with pagination
- Parse RE/MAX specific HTML structure
- Handle pagination through pageNumber parameter
- Extract property data in PropertyListing format
- Implement same error handling and retry logic as MeadowNorthScraper

### Enhanced Monitoring Service
```python
class MonitoringService:
    def __init__(self, config_manager, scrapers: List[BaseScraper], address_matcher, data_dir)
    def _fetch_and_check_listings(self) -> List[PropertyListing]
    def _aggregate_listings_from_all_sources(self) -> List[PropertyListing]
    def _tag_listings_with_source(self, listings: List[PropertyListing], source: str) -> List[PropertyListing]
```

**Enhanced Responsibilities:**
- Manage multiple scraper instances
- Aggregate listings from all sources
- Handle partial failures (one scraper fails, others continue)
- Tag listings with source information
- Maintain existing interface for backward compatibility

### Base Scraper Interface
```python
from abc import ABC, abstractmethod

class BaseScraper(ABC):
    @abstractmethod
    def fetch_listings(self) -> List[PropertyListing]:
        pass
    
    @abstractmethod
    def get_source_name(self) -> str:
        pass
```

**Purpose:**
- Ensure consistent interface across all scrapers
- Enable easy addition of future scrapers
- Provide type safety and documentation

## Data Models

### Enhanced PropertyListing
```python
@dataclass
class PropertyListing:
    address: str
    price: str
    mls_number: str
    description: str
    source: str = ""  # New field to track data source
    square_footage: Optional[str] = None
    bedrooms: Optional[int] = None
    bathrooms: Optional[int] = None
    lot_size: Optional[str] = None
    year_built: Optional[str] = None
    image_url: str = ""
    listing_url: str = ""
    first_seen: datetime = field(default_factory=datetime.now)
```

**Changes:**
- Added `source` field to identify which website the listing came from
- Maintains backward compatibility with existing code

## RE/MAX Website Analysis

Based on the URL pattern `https://www.remax.ca/sk/meadow-lake-real-estate?lang=en&pageNumber=1`, the RE/MAX scraper needs to handle:

### URL Structure
- Base URL: `https://www.remax.ca/sk/meadow-lake-real-estate`
- Query parameters: `lang=en&pageNumber={page_number}`
- Pagination: Increment pageNumber from 1 until no more results

### Expected HTML Structure (to be determined during implementation)
The scraper will need to identify:
- Property container elements
- Price elements and format
- Address elements
- MLS number format
- Description text
- Property details (bedrooms, bathrooms, etc.)
- Image URLs
- Listing detail page URLs

### Pagination Handling
```python
def _handle_pagination(self) -> List[str]:
    """
    Fetch all pages of results from RE/MAX website
    Returns list of HTML content from all pages
    """
    pages = []
    page_number = 1
    
    while True:
        url = f"{self.base_url}?lang=en&pageNumber={page_number}"
        response = self._fetch_page(url)
        
        if self._is_empty_page(response.text):
            break
            
        pages.append(response.text)
        page_number += 1
        
        # Rate limiting between page requests
        self._rate_limit()
    
    return pages
```

## Error Handling

### Multi-Source Error Resilience
- If one scraper fails, others continue operating
- Partial results are still useful to the user
- Failed scrapers are retried independently
- Error notifications specify which source failed

### RE/MAX Specific Error Handling
- Handle RE/MAX website structure changes
- Manage pagination edge cases (empty pages, server errors)
- Parse errors for individual listings don't stop entire scrape
- Network timeouts handled with exponential backoff

### Logging Strategy
```python
# Source-specific logging
self.logger.info(f"[RE/MAX] Fetched {len(listings)} listings from {pages_scraped} pages")
self.logger.warning(f"[RE/MAX] Failed to parse property on page {page_num}: {error}")
self.logger.error(f"[RE/MAX] Scraper failed completely: {error}")

# Aggregated logging
self.logger.info(f"Total listings: {total} (MeadowNorth: {mn_count}, RE/MAX: {remax_count})")
```

## Implementation Details

### File Structure
```
backend/
├── scraper.py (existing MeadowNorthScraper)
├── remax_scraper.py (new RE/MAX scraper)
├── base_scraper.py (new abstract base class)
├── monitoring_service.py (enhanced)
└── app.py (minimal changes for initialization)
```

### Integration Points

#### 1. Scraper Initialization (app.py)
```python
# Initialize scrapers
self.meadow_north_scraper = MeadowNorthScraper()
self.remax_scraper = RemaxScraper()
scrapers = [self.meadow_north_scraper, self.remax_scraper]

# Initialize monitoring service with multiple scrapers
self.monitoring_service = MonitoringService(
    self.config_manager,
    scrapers,  # Pass list instead of single scraper
    self.address_matcher
)
```

#### 2. Monitoring Service Enhancement
```python
def _fetch_and_check_listings(self) -> List[PropertyListing]:
    all_listings = []
    
    for scraper in self.scrapers:
        try:
            source_listings = scraper.fetch_listings()
            # Tag with source
            for listing in source_listings:
                listing.source = scraper.get_source_name()
            all_listings.extend(source_listings)
            
        except Exception as e:
            self.logger.error(f"Scraper {scraper.get_source_name()} failed: {e}")
            # Continue with other scrapers
            continue
    
    self.current_listings = all_listings
    # ... rest of existing logic
```

#### 3. Frontend Display Enhancement
The existing web interface will automatically show listings from both sources, with the source field displayed to users.

### Rate Limiting and Respectful Scraping
- Implement same rate limiting as existing scraper (1 second between requests)
- Add additional delay between pagination requests
- Use appropriate User-Agent headers
- Monitor for rate limiting responses from RE/MAX

### Testing Strategy

#### Unit Tests
- **RemaxScraper**: Test HTML parsing with mock RE/MAX HTML
- **Pagination**: Test page detection and stopping conditions
- **Error Handling**: Test individual scraper failures
- **Data Extraction**: Test RE/MAX specific field extraction

#### Integration Tests
- **Multi-Source Monitoring**: Test monitoring with both scrapers
- **Partial Failure**: Test behavior when one scraper fails
- **Address Matching**: Test matching across different sources
- **Source Attribution**: Verify listings are properly tagged

#### Manual Testing
- **Live Website Testing**: Verify scraping works with current RE/MAX site
- **Pagination Testing**: Ensure all pages are scraped
- **Performance Testing**: Monitor impact of multiple scrapers

## Migration Strategy

### Phase 1: Base Infrastructure
1. Create base scraper interface
2. Refactor existing MeadowNorthScraper to implement interface
3. Update monitoring service to accept multiple scrapers
4. Add source field to PropertyListing model

### Phase 2: RE/MAX Implementation
1. Implement RemaxScraper class
2. Add RE/MAX scraper to monitoring service initialization
3. Test with both scrapers running

### Phase 3: Frontend Enhancement
1. Update frontend to display source information
2. Add source filtering capabilities (optional)
3. Update notifications to include source information

### Backward Compatibility
- Existing configuration and data files remain unchanged
- API responses maintain same structure with added source field
- No changes required to address matching or notification logic

## Performance Considerations

### Concurrent Scraping
- Scrapers run sequentially to avoid overwhelming target websites
- Each scraper maintains its own rate limiting
- Total monitoring time increases but remains reasonable

### Memory Usage
- Listings from all sources stored in memory during processing
- Consider pagination for very large result sets
- Existing data persistence handles increased volume

### Network Impact
- Double the network requests (two websites instead of one)
- Staggered requests to avoid simultaneous load
- Existing retry and backoff logic prevents excessive requests

## Security Considerations

### Website Terms of Service
- Review RE/MAX robots.txt and terms of service
- Implement respectful scraping practices
- Monitor for anti-scraping measures

### Data Handling
- Same privacy considerations as existing scraper
- No additional personal data collected
- Existing data persistence security applies

### Error Information
- Avoid logging sensitive website structure details
- Sanitize error messages in user-facing notifications
- Maintain existing logging security practices