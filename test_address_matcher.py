"""
Unit tests for the AddressMatcher class.
Tests address normalization, fuzzy matching, and various edge cases.
"""
import unittest
from datetime import datetime
from shared.address_matcher import AddressMatcher
from shared.models import PropertyListing


class TestAddressMatcher(unittest.TestCase):
    """Test cases for AddressMatcher functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.matcher = AddressMatcher()
        self.high_threshold_matcher = AddressMatcher(similarity_threshold=0.95)
        self.low_threshold_matcher = AddressMatcher(similarity_threshold=0.6)
        
        # Sample property listings for testing
        self.sample_listings = [
            PropertyListing(
                address="123 Main Street",
                price="$500,000",
                mls_number="ML123456",
                description="Beautiful home"
            ),
            PropertyListing(
                address="456 Centre Ave",
                price="$600,000",
                mls_number="ML234567",
                description="Modern condo"
            ),
            PropertyListing(
                address="789 Oak Road North",
                price="$750,000",
                mls_number="ML345678",
                description="Family home"
            ),
            PropertyListing(
                address="321 Pine Dr.",
                price="$400,000",
                mls_number="ML456789",
                description="Cozy house"
            )
        ]
    
    def test_init_valid_threshold(self):
        """Test AddressMatcher initialization with valid threshold"""
        matcher = AddressMatcher(0.7)
        self.assertEqual(matcher.similarity_threshold, 0.7)
    
    def test_init_invalid_threshold(self):
        """Test AddressMatcher initialization with invalid threshold"""
        with self.assertRaises(ValueError):
            AddressMatcher(-0.1)
        
        with self.assertRaises(ValueError):
            AddressMatcher(1.1)
    
    def test_normalize_address_basic(self):
        """Test basic address normalization"""
        test_cases = [
            ("123 Main St", "123 MAIN STREET"),
            ("456 Centre Ave", "456 CENTRE AVENUE"),
            ("789 Oak Rd", "789 OAK ROAD"),
            ("321 Pine Dr", "321 PINE DRIVE"),
            ("555 Elm Ct", "555 ELM COURT"),
            ("777 Maple Pl", "777 MAPLE PLACE"),
            ("999 Cedar Ln", "999 CEDAR LANE"),
            ("111 Birch Way", "111 BIRCH WAY"),
            ("222 Spruce Blvd", "222 SPRUCE BOULEVARD"),
            ("333 Willow Cir", "333 WILLOW CIRCLE")
        ]
        
        for input_addr, expected in test_cases:
            with self.subTest(input_addr=input_addr):
                result = self.matcher.normalize_address(input_addr)
                self.assertEqual(result, expected)
    
    def test_normalize_address_case_insensitive(self):
        """Test case insensitive normalization"""
        test_cases = [
            ("123 main street", "123 MAIN STREET"),
            ("456 CENTRE AVENUE", "456 CENTRE AVENUE"),
            ("789 Oak Road", "789 OAK ROAD"),
            ("321 pine DRIVE", "321 PINE DRIVE")
        ]
        
        for input_addr, expected in test_cases:
            with self.subTest(input_addr=input_addr):
                result = self.matcher.normalize_address(input_addr)
                self.assertEqual(result, expected)
    
    def test_normalize_address_whitespace(self):
        """Test whitespace normalization"""
        test_cases = [
            ("  123   Main   Street  ", "123 MAIN STREET"),
            ("456\tCentre\tAvenue", "456 CENTRE AVENUE"),
            ("789\nOak\nRoad", "789 OAK ROAD"),
            ("321  Pine   Dr  ", "321 PINE DRIVE")
        ]
        
        for input_addr, expected in test_cases:
            with self.subTest(input_addr=input_addr):
                result = self.matcher.normalize_address(input_addr)
                self.assertEqual(result, expected)
    
    def test_normalize_address_punctuation(self):
        """Test punctuation removal"""
        test_cases = [
            ("123 Main St.", "123 MAIN STREET"),
            ("456 Centre Ave,", "456 CENTRE AVENUE"),
            ("789, Oak Road", "789 OAK ROAD"),
            ("321 Pine Dr.,", "321 PINE DRIVE")
        ]
        
        for input_addr, expected in test_cases:
            with self.subTest(input_addr=input_addr):
                result = self.matcher.normalize_address(input_addr)
                self.assertEqual(result, expected)
    
    def test_normalize_address_directions(self):
        """Test direction abbreviation normalization"""
        test_cases = [
            ("123 Main St N", "123 MAIN STREET NORTH"),
            ("456 Centre Ave S", "456 CENTRE AVENUE SOUTH"),
            ("789 Oak Rd E", "789 OAK ROAD EAST"),
            ("321 Pine Dr W", "321 PINE DRIVE WEST"),
            ("555 Elm St NE", "555 ELM STREET NORTHEAST"),
            ("777 Maple Ave NW", "777 MAPLE AVENUE NORTHWEST"),
            ("999 Cedar Rd SE", "999 CEDAR ROAD SOUTHEAST"),
            ("111 Birch Dr SW", "111 BIRCH DRIVE SOUTHWEST")
        ]
        
        for input_addr, expected in test_cases:
            with self.subTest(input_addr=input_addr):
                result = self.matcher.normalize_address(input_addr)
                self.assertEqual(result, expected)
    
    def test_normalize_address_empty_invalid(self):
        """Test normalization with empty or invalid input"""
        test_cases = [
            ("", ""),
            (None, ""),
            ("   ", ""),
            (123, "")  # Non-string input
        ]
        
        for input_addr, expected in test_cases:
            with self.subTest(input_addr=input_addr):
                result = self.matcher.normalize_address(input_addr)
                self.assertEqual(result, expected)
    
    def test_calculate_similarity_exact_match(self):
        """Test similarity calculation for exact matches"""
        test_cases = [
            ("123 Main Street", "123 Main Street"),
            ("456 Centre Ave", "456 Centre Avenue"),
            ("789 oak road", "789 OAK ROAD")
        ]
        
        for addr1, addr2 in test_cases:
            with self.subTest(addr1=addr1, addr2=addr2):
                similarity = self.matcher.calculate_similarity(addr1, addr2)
                self.assertEqual(similarity, 1.0)
    
    def test_calculate_similarity_substring_match(self):
        """Test similarity calculation for substring matches"""
        test_cases = [
            ("123 Main St", "123 Main Street North"),
            ("Centre Ave", "456 Centre Avenue"),
            ("Oak Road", "789 Oak Road North")
        ]
        
        for addr1, addr2 in test_cases:
            with self.subTest(addr1=addr1, addr2=addr2):
                similarity = self.matcher.calculate_similarity(addr1, addr2)
                self.assertEqual(similarity, 0.95)
    
    def test_calculate_similarity_fuzzy_match(self):
        """Test similarity calculation for fuzzy matches"""
        # These should have high similarity due to normalization
        # Note: St/Street, Ave/Avenue, Rd/Road normalize to the same thing
        normalized_cases = [
            ("123 Main Street", "123 Main St"),
            ("456 Centre Avenue", "456 Centre Ave"),
            ("789 Oak Road", "789 Oak Rd")
        ]
        
        for addr1, addr2 in normalized_cases:
            with self.subTest(addr1=addr1, addr2=addr2):
                similarity = self.matcher.calculate_similarity(addr1, addr2)
                self.assertEqual(similarity, 1.0)  # Perfect match after normalization
        
        # Test actual fuzzy matches (minor typos/differences)
        fuzzy_cases = [
            ("123 Main Street", "123 Main Stret"),  # Typo
            ("456 Centre Avenue", "456 Center Avenue"),  # Different spelling
            ("789 Oak Road", "789 Oak Road North")  # Extra word
        ]
        
        for addr1, addr2 in fuzzy_cases:
            with self.subTest(addr1=addr1, addr2=addr2):
                similarity = self.matcher.calculate_similarity(addr1, addr2)
                self.assertGreater(similarity, 0.8)
                self.assertLess(similarity, 1.0)
    
    def test_calculate_similarity_no_match(self):
        """Test similarity calculation for non-matching addresses"""
        test_cases = [
            ("123 Main Street", "456 Oak Road"),
            ("789 Centre Avenue", "321 Pine Drive"),
            ("555 Elm Court", "777 Maple Way")
        ]
        
        for addr1, addr2 in test_cases:
            with self.subTest(addr1=addr1, addr2=addr2):
                similarity = self.matcher.calculate_similarity(addr1, addr2)
                self.assertLess(similarity, 0.5)
    
    def test_calculate_similarity_empty_input(self):
        """Test similarity calculation with empty input"""
        test_cases = [
            ("", "123 Main Street"),
            ("123 Main Street", ""),
            ("", ""),
            (None, "123 Main Street"),
            ("123 Main Street", None)
        ]
        
        for addr1, addr2 in test_cases:
            with self.subTest(addr1=addr1, addr2=addr2):
                similarity = self.matcher.calculate_similarity(addr1, addr2)
                self.assertEqual(similarity, 0.0)
    
    def test_is_match_default_threshold(self):
        """Test is_match with default threshold (0.8)"""
        # Should match
        self.assertTrue(self.matcher.is_match("123 Main St", "123 Main Street"))
        self.assertTrue(self.matcher.is_match("456 Centre Ave", "456 Centre Avenue"))
        
        # Should not match
        self.assertFalse(self.matcher.is_match("123 Main Street", "456 Oak Road"))
        self.assertFalse(self.matcher.is_match("789 Centre Avenue", "321 Pine Drive"))
    
    def test_is_match_different_thresholds(self):
        """Test is_match with different similarity thresholds"""
        addr1 = "123 Main Street"
        addr2 = "123 Main St"  # High similarity but not exact
        
        # Should match with low threshold
        self.assertTrue(self.low_threshold_matcher.is_match(addr1, addr2))
        
        # Should match with default threshold
        self.assertTrue(self.matcher.is_match(addr1, addr2))
        
        # May not match with very high threshold
        # (depends on exact similarity score)
        similarity = self.matcher.calculate_similarity(addr1, addr2)
        expected_match = similarity >= 0.95
        self.assertEqual(self.high_threshold_matcher.is_match(addr1, addr2), expected_match)
    
    def test_find_matches_basic(self):
        """Test finding matches in a list of listings"""
        target = "123 Main Street"
        matches = self.matcher.find_matches(target, self.sample_listings)
        
        # Should find the exact match
        self.assertEqual(len(matches), 1)
        self.assertEqual(matches[0][0].address, "123 Main Street")
        self.assertEqual(matches[0][1], 1.0)  # Perfect match
    
    def test_find_matches_fuzzy(self):
        """Test finding fuzzy matches"""
        target = "123 Main St"  # Abbreviated version
        matches = self.matcher.find_matches(target, self.sample_listings)
        
        # Should find the match with high similarity
        self.assertEqual(len(matches), 1)
        self.assertEqual(matches[0][0].address, "123 Main Street")
        self.assertGreater(matches[0][1], 0.8)
    
    def test_find_matches_multiple(self):
        """Test finding multiple matches with low threshold"""
        # Add a similar address to test multiple matches
        similar_listing = PropertyListing(
            address="123 Main St",
            price="$550,000",
            mls_number="ML567890",
            description="Similar home"
        )
        listings_with_similar = self.sample_listings + [similar_listing]
        
        target = "123 Main Street"
        matches = self.low_threshold_matcher.find_matches(target, listings_with_similar)
        
        # Should find both similar addresses
        self.assertGreaterEqual(len(matches), 2)
        
        # Should be sorted by similarity (highest first)
        for i in range(len(matches) - 1):
            self.assertGreaterEqual(matches[i][1], matches[i + 1][1])
    
    def test_find_matches_no_matches(self):
        """Test finding matches when none exist"""
        target = "999 Nonexistent Street"
        matches = self.matcher.find_matches(target, self.sample_listings)
        
        self.assertEqual(len(matches), 0)
    
    def test_find_matches_empty_input(self):
        """Test finding matches with empty input"""
        # Empty target address
        matches = self.matcher.find_matches("", self.sample_listings)
        self.assertEqual(len(matches), 0)
        
        # Empty listings
        matches = self.matcher.find_matches("123 Main Street", [])
        self.assertEqual(len(matches), 0)
        
        # Both empty
        matches = self.matcher.find_matches("", [])
        self.assertEqual(len(matches), 0)
    
    def test_find_best_match(self):
        """Test finding the best match"""
        target = "123 Main Street"
        best_match = self.matcher.find_best_match(target, self.sample_listings)
        
        self.assertIsNotNone(best_match)
        self.assertEqual(best_match[0].address, "123 Main Street")
        self.assertEqual(best_match[1], 1.0)
    
    def test_find_best_match_no_matches(self):
        """Test finding best match when none exist"""
        target = "999 Nonexistent Street"
        best_match = self.matcher.find_best_match(target, self.sample_listings)
        
        self.assertIsNone(best_match)
    
    def test_get_similarity_explanation(self):
        """Test getting detailed similarity explanation"""
        target = "123 Main St"
        listing = "123 Main Street"
        
        explanation = self.matcher.get_similarity_explanation(target, listing)
        
        # Check all expected keys are present
        expected_keys = [
            'original_target', 'original_listing',
            'normalized_target', 'normalized_listing',
            'similarity_score', 'is_match', 'threshold'
        ]
        
        for key in expected_keys:
            self.assertIn(key, explanation)
        
        # Check values
        self.assertEqual(explanation['original_target'], target)
        self.assertEqual(explanation['original_listing'], listing)
        self.assertEqual(explanation['normalized_target'], "123 MAIN STREET")
        self.assertEqual(explanation['normalized_listing'], "123 MAIN STREET")
        self.assertEqual(explanation['similarity_score'], 1.0)
        self.assertTrue(explanation['is_match'])
        self.assertEqual(explanation['threshold'], 0.8)
    
    def test_address_variations_real_world(self):
        """Test real-world address variations"""
        base_address = "123 Centre Street"
        variations = [
            "123 Centre St",
            "123 CENTRE STREET",
            "123 centre street",
            "123  Centre   Street",
            "123 Centre St.",
            "123 Centre Street North",
            "Centre Street 123"  # This should have lower similarity
        ]
        
        for variation in variations[:-1]:  # Exclude the last one
            with self.subTest(variation=variation):
                similarity = self.matcher.calculate_similarity(base_address, variation)
                self.assertGreaterEqual(similarity, 0.8, 
                                      f"Failed for variation: {variation}")
        
        # The last variation should have lower similarity
        similarity = self.matcher.calculate_similarity(base_address, variations[-1])
        self.assertLess(similarity, 0.8)
    
    def test_typo_handling(self):
        """Test handling of minor typos"""
        base_address = "123 Main Street"
        typos = [
            "123 Main Stret",    # Missing 'e'
            "123 Mian Street",   # Transposed letters
            "123 Main Streeet",  # Extra letter
            "12 Main Street"     # Missing digit
        ]
        
        for typo in typos:
            with self.subTest(typo=typo):
                similarity = self.matcher.calculate_similarity(base_address, typo)
                # Should have reasonable similarity but not perfect
                self.assertGreater(similarity, 0.6)
                self.assertLess(similarity, 1.0)


if __name__ == '__main__':
    unittest.main()