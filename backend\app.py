"""
Flask backend API server for the Real Estate Monitor application.
"""
from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from shared.config_manager import Config<PERSON>anager
from shared.models import Config, PropertyListing
from shared.address_matcher import AddressMatcher
from shared.notification_manager import NotificationManager
from backend.scraper import MeadowNorthScraper
from backend.monitoring_service import MonitoringService, MonitoringEvent


class RealEstateMonitorAPI:
    """Flask API server for the Real Estate Monitor"""
    
    def __init__(self):
        import os
        
        # Get the directory containing the backend folder
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(backend_dir)
        frontend_dir = os.path.join(project_root, 'frontend')
        
        # Create Flask app with custom static folder
        self.app = Flask(__name__, static_folder=frontend_dir, static_url_path='/static')
        CORS(self.app)  # Enable CORS for frontend communication
        
        # Initialize SocketIO for WebSocket support
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.scraper = MeadowNorthScraper()
        self.address_matcher = AddressMatcher()
        
        # Initialize notification manager
        self.notification_manager = NotificationManager()
        self.notification_manager.add_websocket_callback(self._send_websocket_notification)
        
        # Initialize monitoring service
        self.monitoring_service = MonitoringService(
            self.config_manager,
            self.scraper,
            self.address_matcher
        )
        
        # Register for monitoring events
        self.monitoring_service.add_event_callback(self._handle_monitoring_event)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Register routes and WebSocket events
        self._register_routes()
        self._register_websocket_events()
        self._register_error_handlers()
    
    def _handle_monitoring_event(self, event: MonitoringEvent):
        """
        Handle monitoring events from the monitoring service.
        
        Args:
            event: MonitoringEvent that occurred
        """
        # Log the event
        if event.event_type == "property_found":
            self.logger.info(f"Property found event: {event.message}")
            # Send property found notification
            if event.property_listing:
                self.notification_manager.notify_property_found(event.property_listing)
        elif event.event_type == "property_removed":
            self.logger.info(f"Property removed event: {event.message}")
            # Send property removed notification
            # Extract MLS number from message (format: "Target property removed: MLS_NUMBER")
            mls_number = event.message.split(": ")[-1] if ": " in event.message else "Unknown"
            self.notification_manager.notify_property_removed(mls_number)
        elif event.event_type == "error":
            self.logger.error(f"Monitoring error: {event.message}")
            # Send error notification
            self.notification_manager.notify_error(event.message, event.error)
        elif event.event_type == "status_change":
            self.logger.info(f"Status change: {event.message}")
            # Send status update notification
            self.notification_manager.notify_status_update(event.message)
    
    def _send_websocket_notification(self, notification_data: Dict[str, Any]):
        """
        Send notification data via WebSocket to all connected clients.
        
        Args:
            notification_data: Dictionary containing notification data
        """
        try:
            self.socketio.emit('notification', notification_data)
            self.logger.debug(f"WebSocket notification sent: {notification_data['notification_type']}")
        except Exception as e:
            self.logger.error(f"Error sending WebSocket notification: {e}")
    
    def _register_websocket_events(self):
        """Register WebSocket event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            self.logger.info("Client connected to WebSocket")
            # Send current status to newly connected client
            try:
                config = self.config_manager.load_config()
                status_data = {
                    "type": "status_update",
                    "monitoring_active": self.monitoring_service.is_running(),
                    "monitoring_status": self.monitoring_service.get_status(),
                    "target_address": config.target_address,
                    "last_check_time": self.monitoring_service.get_last_check_time().isoformat() 
                                     if self.monitoring_service.get_last_check_time() else None
                }
                emit('status_update', status_data)
            except Exception as e:
                self.logger.error(f"Error sending initial status to client: {e}")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            self.logger.info("Client disconnected from WebSocket")
        
        @self.socketio.on('request_status')
        def handle_status_request():
            """Handle client request for current status"""
            try:
                config = self.config_manager.load_config()
                all_listings = self.monitoring_service.get_current_listings()
                matching_listings = self.monitoring_service.get_matching_listings()
                
                status_data = {
                    "type": "status_update",
                    "monitoring_active": self.monitoring_service.is_running(),
                    "monitoring_status": self.monitoring_service.get_status(),
                    "target_address": config.target_address,
                    "last_check_time": self.monitoring_service.get_last_check_time().isoformat() 
                                     if self.monitoring_service.get_last_check_time() else None,
                    "total_listings_count": len(all_listings),
                    "matching_listings_count": len(matching_listings),
                    "found_listings_count": len(config.found_listings)
                }
                emit('status_update', status_data)
            except Exception as e:
                self.logger.error(f"Error handling status request: {e}")
                emit('error', {"message": "Failed to get status"})
        
        @self.socketio.on('test_notification')
        def handle_test_notification():
            """Handle test notification request (for development/testing)"""
            try:
                self.notification_manager.notify_status_update("Test notification sent successfully", "success")
                emit('test_response', {"success": True, "message": "Test notification sent"})
            except Exception as e:
                self.logger.error(f"Error sending test notification: {e}")
                emit('test_response', {"success": False, "message": str(e)})
    
    def _register_routes(self):
        """Register all API routes"""
        
        @self.app.route('/')
        def serve_frontend():
            """Serve the frontend HTML file"""
            try:
                from flask import send_from_directory
                import os
                
                # Get the directory containing the backend folder
                backend_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(backend_dir)
                frontend_dir = os.path.join(project_root, 'frontend')
                
                return send_from_directory(frontend_dir, 'index.html')
            except Exception as e:
                self.logger.error(f"Error serving frontend: {e}")
                return f"""
                <html>
                <head><title>Real Estate Monitor</title></head>
                <body>
                    <h1>Real Estate Monitor</h1>
                    <p>Error loading frontend: {e}</p>
                    <p>API is available at:</p>
                    <ul>
                        <li><a href="/api/health">Health Check</a></li>
                        <li><a href="/api/config">Configuration</a></li>
                        <li><a href="/api/status">Status</a></li>
                    </ul>
                </body>
                </html>
                """, 500
        

        @self.app.route('/api/config', methods=['GET'])
        def get_config():
            """Get current configuration"""
            try:
                config = self.config_manager.load_config()
                return jsonify({
                    'success': True,
                    'data': {
                        'target_address': config.target_address,
                        'check_interval_minutes': config.check_interval_minutes,
                        'notification_enabled': config.notification_enabled,
                        'last_check': config.last_check.isoformat() if config.last_check else None,
                        'found_listings': config.found_listings,
                        'needs_setup': self.config_manager.needs_address_setup()
                    }
                })
            except Exception as e:
                self.logger.error(f"Error getting config: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/config', methods=['POST'])
        def update_config():
            """Update configuration"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({
                        'success': False,
                        'error': 'No data provided'
                    }), 400
                
                # Load current config
                config = self.config_manager.load_config()
                
                # Update fields if provided
                if 'target_address' in data:
                    if not data['target_address'] or not data['target_address'].strip():
                        return jsonify({
                            'success': False,
                            'error': 'Target address cannot be empty'
                        }), 400
                    
                    if not self.config_manager.validate_address_format(data['target_address']):
                        return jsonify({
                            'success': False,
                            'error': 'Invalid address format'
                        }), 400
                    
                    config.target_address = data['target_address'].strip()
                
                if 'check_interval_minutes' in data:
                    interval = data['check_interval_minutes']
                    if not isinstance(interval, int) or interval <= 0:
                        return jsonify({
                            'success': False,
                            'error': 'Check interval must be a positive integer'
                        }), 400
                    config.check_interval_minutes = interval
                
                if 'notification_enabled' in data:
                    config.notification_enabled = bool(data['notification_enabled'])
                
                # Save updated config
                self.config_manager.save_config(config)
                
                return jsonify({
                    'success': True,
                    'message': 'Configuration updated successfully'
                })
                
            except ValueError as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 400
            except Exception as e:
                self.logger.error(f"Error updating config: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            """Get current monitoring status"""
            try:
                config = self.config_manager.load_config()
                last_check_time = self.monitoring_service.get_last_check_time()
                all_listings = self.monitoring_service.get_current_listings()
                matching_listings = self.monitoring_service.get_matching_listings()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'monitoring_active': self.monitoring_service.is_running(),
                        'monitoring_status': self.monitoring_service.get_status(),
                        'last_check_time': last_check_time.isoformat() if last_check_time else None,
                        'target_address': config.target_address,
                        'check_interval_minutes': config.check_interval_minutes,
                        'total_listings_count': len(all_listings),
                        'matching_listings_count': len(matching_listings),
                        'found_listings_count': len(config.found_listings),
                        'needs_setup': self.config_manager.needs_address_setup()
                    }
                })
            except Exception as e:
                self.logger.error(f"Error getting status: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/listings', methods=['GET'])
        def get_listings():
            """Get current property listings that match the target address"""
            try:
                current_listings = self.monitoring_service.get_matching_listings()
                last_check_time = self.monitoring_service.get_last_check_time()
                
                # Convert PropertyListing objects to dictionaries
                listings_data = []
                for listing in current_listings:
                    listings_data.append({
                        'address': listing.address,
                        'price': listing.price,
                        'mls_number': listing.mls_number,
                        'description': listing.description,
                        'square_footage': listing.square_footage,
                        'bedrooms': listing.bedrooms,
                        'bathrooms': listing.bathrooms,
                        'lot_size': listing.lot_size,
                        'year_built': listing.year_built,
                        'image_url': listing.image_url,
                        'listing_url': listing.listing_url,
                        'first_seen': listing.first_seen.isoformat()
                    })
                
                return jsonify({
                    'success': True,
                    'data': {
                        'listings': listings_data,
                        'count': len(listings_data),
                        'last_updated': last_check_time.isoformat() if last_check_time else None
                    }
                })
            except Exception as e:
                self.logger.error(f"Error getting listings: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/start-monitoring', methods=['POST'])
        def start_monitoring():
            """Start the monitoring service"""
            try:
                if self.monitoring_service.is_running():
                    return jsonify({
                        'success': False,
                        'error': 'Monitoring is already active'
                    }), 400
                
                # Start monitoring service
                if self.monitoring_service.start_monitoring():
                    return jsonify({
                        'success': True,
                        'message': 'Monitoring started successfully'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Failed to start monitoring service'
                    }), 500
                
            except Exception as e:
                self.logger.error(f"Error starting monitoring: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/stop-monitoring', methods=['POST'])
        def stop_monitoring():
            """Stop the monitoring service"""
            try:
                if not self.monitoring_service.is_running():
                    return jsonify({
                        'success': False,
                        'error': 'Monitoring is not active'
                    }), 400
                
                # Stop monitoring service
                if self.monitoring_service.stop_monitoring():
                    return jsonify({
                        'success': True,
                        'message': 'Monitoring stopped successfully'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Failed to stop monitoring service'
                    }), 500
                
            except Exception as e:
                self.logger.error(f"Error stopping monitoring: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/check-now', methods=['POST'])
        def check_now():
            """Perform an immediate check for listings"""
            try:
                if self.config_manager.needs_address_setup():
                    return jsonify({
                        'success': False,
                        'error': 'Please configure target address before checking'
                    }), 400
                
                # Perform immediate check using monitoring service
                matching_listings = self.monitoring_service.perform_immediate_check()
                all_listings = self.monitoring_service.get_current_listings()
                last_check_time = self.monitoring_service.get_last_check_time()
                
                return jsonify({
                    'success': True,
                    'message': f'Check completed. Found {len(matching_listings)} matching listings out of {len(all_listings)} total.',
                    'data': {
                        'matching_listings_count': len(matching_listings),
                        'total_listings_count': len(all_listings),
                        'check_time': last_check_time.isoformat() if last_check_time else None
                    }
                })
                
            except Exception as e:
                self.logger.error(f"Error performing immediate check: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/notifications/settings', methods=['GET'])
        def get_notification_settings():
            """Get current notification settings"""
            try:
                settings = self.notification_manager.get_notification_settings()
                return jsonify({
                    'success': True,
                    'data': settings
                })
            except Exception as e:
                self.logger.error(f"Error getting notification settings: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/notifications/settings', methods=['POST'])
        def update_notification_settings():
            """Update notification settings"""
            try:
                try:
                    data = request.get_json()
                except Exception:
                    return jsonify({
                        'success': False,
                        'error': 'Invalid JSON data'
                    }), 400
                
                if data is None:
                    return jsonify({
                        'success': False,
                        'error': 'No data provided'
                    }), 400
                
                # Update desktop notifications setting
                if 'desktop_notifications_enabled' in data:
                    self.notification_manager.set_desktop_notifications_enabled(
                        bool(data['desktop_notifications_enabled'])
                    )
                
                # Update web notifications setting
                if 'web_notifications_enabled' in data:
                    self.notification_manager.set_web_notifications_enabled(
                        bool(data['web_notifications_enabled'])
                    )
                
                return jsonify({
                    'success': True,
                    'message': 'Notification settings updated successfully'
                })
                
            except Exception as e:
                self.logger.error(f"Error updating notification settings: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/notifications/test', methods=['POST'])
        def test_notifications():
            """Send test notifications"""
            try:
                data = request.get_json() or {}
                notification_type = data.get('type', 'status')
                
                if notification_type == 'property_found':
                    # Create a test property listing
                    from shared.models import PropertyListing
                    test_listing = PropertyListing(
                        address="123 Test Street",
                        price="$500,000",
                        mls_number="TEST123",
                        description="This is a test property listing for notification testing.",
                        bedrooms=3,
                        bathrooms=2,
                        square_footage="1,500 sq ft"
                    )
                    self.notification_manager.notify_property_found(test_listing)
                    message = "Test property found notification sent"
                    
                elif notification_type == 'property_removed':
                    self.notification_manager.notify_property_removed("TEST123", "123 Test Street")
                    message = "Test property removed notification sent"
                    
                elif notification_type == 'error':
                    self.notification_manager.notify_error("This is a test error notification")
                    message = "Test error notification sent"
                    
                else:  # status
                    self.notification_manager.notify_status_update("This is a test status notification", "success")
                    message = "Test status notification sent"
                
                return jsonify({
                    'success': True,
                    'message': message
                })
                
            except Exception as e:
                self.logger.error(f"Error sending test notification: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """Health check endpoint for monitoring application status"""
            try:
                health_status = {
                    'status': 'healthy',
                    'timestamp': datetime.now().isoformat(),
                    'components': {}
                }
                
                # Check configuration manager
                try:
                    config = self.config_manager.load_config()
                    health_status['components']['config_manager'] = {
                        'status': 'healthy',
                        'has_target_address': bool(config.target_address)
                    }
                except Exception as e:
                    health_status['components']['config_manager'] = {
                        'status': 'unhealthy',
                        'error': str(e)
                    }
                    health_status['status'] = 'degraded'
                
                # Check monitoring service
                try:
                    monitoring_status = self.monitoring_service.get_status()
                    health_status['components']['monitoring_service'] = {
                        'status': 'healthy' if monitoring_status != 'error' else 'unhealthy',
                        'monitoring_active': self.monitoring_service.is_running(),
                        'current_status': monitoring_status,
                        'last_check': self.monitoring_service.get_last_check_time().isoformat() 
                                     if self.monitoring_service.get_last_check_time() else None
                    }
                except Exception as e:
                    health_status['components']['monitoring_service'] = {
                        'status': 'unhealthy',
                        'error': str(e)
                    }
                    health_status['status'] = 'degraded'
                
                # Check scraper connectivity (basic test)
                try:
                    # Don't actually fetch listings, just check if scraper is configured
                    health_status['components']['scraper'] = {
                        'status': 'healthy',
                        'base_url': self.scraper.base_url if hasattr(self.scraper, 'base_url') else 'unknown'
                    }
                except Exception as e:
                    health_status['components']['scraper'] = {
                        'status': 'unhealthy',
                        'error': str(e)
                    }
                    health_status['status'] = 'degraded'
                
                # Determine overall status
                component_statuses = [comp['status'] for comp in health_status['components'].values()]
                if 'unhealthy' in component_statuses:
                    health_status['status'] = 'unhealthy'
                elif 'degraded' in component_statuses:
                    health_status['status'] = 'degraded'
                
                # Return appropriate HTTP status code
                if health_status['status'] == 'healthy':
                    return jsonify(health_status), 200
                elif health_status['status'] == 'degraded':
                    return jsonify(health_status), 200  # Still return 200 for degraded
                else:
                    return jsonify(health_status), 503  # Service unavailable for unhealthy
                
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                return jsonify({
                    'status': 'unhealthy',
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e)
                }), 503
    
    def _register_error_handlers(self):
        """Register error handlers for the Flask app"""
        
        @self.app.errorhandler(404)
        def not_found(error):
            self.logger.warning(f"404 error: {error}")
            return jsonify({
                'success': False,
                'error': 'Endpoint not found'
            }), 404
        
        @self.app.errorhandler(405)
        def method_not_allowed(error):
            self.logger.warning(f"405 error: {error}")
            return jsonify({
                'success': False,
                'error': 'Method not allowed'
            }), 405
        
        @self.app.errorhandler(400)
        def bad_request(error):
            self.logger.warning(f"400 error: {error}")
            return jsonify({
                'success': False,
                'error': 'Bad request'
            }), 400
        
        @self.app.errorhandler(500)
        def internal_error(error):
            self.logger.error(f"500 error: {error}")
            return jsonify({
                'success': False,
                'error': 'Internal server error'
            }), 500
        
        @self.app.errorhandler(Exception)
        def handle_exception(error):
            self.logger.error(f"Unhandled exception: {str(error)}", exc_info=True)
            
            # Don't expose internal error details in production
            if self.app.debug:
                error_message = str(error)
            else:
                error_message = 'An unexpected error occurred'
            
            return jsonify({
                'success': False,
                'error': error_message
            }), 500
        
        # Handle JSON parsing errors
        @self.app.errorhandler(400)
        def handle_json_error(error):
            if 'JSON' in str(error) or 'json' in str(error):
                return jsonify({
                    'success': False,
                    'error': 'Invalid JSON data'
                }), 400
            return bad_request(error)
    

    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """Run the Flask application with SocketIO support"""
        self.logger.info(f"Starting Real Estate Monitor API server on {host}:{port}")
        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            # Ensure monitoring service is stopped on shutdown
            self.shutdown()
    
    def shutdown(self):
        """Gracefully shutdown the API server and monitoring service"""
        self.logger.info("Shutting down Real Estate Monitor API server...")
        if self.monitoring_service.is_running():
            self.logger.info("Stopping monitoring service...")
            self.monitoring_service.stop_monitoring(timeout=10.0)


# Create the Flask app instance
api = RealEstateMonitorAPI()
app = api.app

if __name__ == '__main__':
    api.run(debug=True)