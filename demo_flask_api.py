"""
Demo script to show the Flask API server functionality.
"""
from backend.app import api
import json

def demo_api_routes():
    """Demonstrate the available API routes"""
    print("Real Estate Monitor Flask API Server")
    print("=" * 40)
    
    with api.app.app_context():
        print("\nAvailable API Routes:")
        for rule in api.app.url_map.iter_rules():
            methods = ', '.join(rule.methods - {'HEAD', 'OPTIONS'})
            print(f"  {rule.rule:<25} [{methods}]")
    
    print("\nAPI Endpoints Description:")
    print("  GET  /api/config          - Get current configuration")
    print("  POST /api/config          - Update configuration")
    print("  GET  /api/status          - Get monitoring status")
    print("  GET  /api/listings        - Get current property listings")
    print("  POST /api/start-monitoring - Start monitoring service")
    print("  POST /api/stop-monitoring  - Stop monitoring service")
    print("  POST /api/check-now        - Perform immediate check")
    
    print("\nFeatures implemented:")
    print("  ✓ Flask application with basic route structure")
    print("  ✓ API endpoints for configuration, status, and listings")
    print("  ✓ CORS support for frontend communication")
    print("  ✓ Error handling middleware for API responses")
    print("  ✓ Background monitoring service")
    print("  ✓ Integration with existing components (ConfigManager, Scraper, AddressMatcher)")
    
    print("\nTo start the server:")
    print("  python backend/app.py")
    print("  Server will run on http://127.0.0.1:5000")

if __name__ == '__main__':
    demo_api_routes()