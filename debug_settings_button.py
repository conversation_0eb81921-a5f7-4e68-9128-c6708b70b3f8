#!/usr/bin/env python3
"""
Debug script to diagnose the settings button issue in the Real Estate Monitor application.
This script will check various components and provide diagnostic information.
"""

import os
import sys
import requests
import subprocess
import time
from pathlib import Path

def check_file_exists(file_path: str) -> bool:
    """Check if a file exists and is readable"""
    path = Path(file_path)
    return path.exists() and path.is_file()

def check_backend_running() -> bool:
    """Check if the Flask backend is running and accessible"""
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        return response.status_code in [200, 503]
    except:
        return False

def check_backend_config_endpoint() -> dict:
    """Test the backend configuration endpoint"""
    try:
        response = requests.get('http://localhost:5000/api/config', timeout=5)
        return {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'data': response.json() if response.status_code == 200 else None,
            'error': None
        }
    except Exception as e:
        return {
            'status_code': None,
            'success': False,
            'data': None,
            'error': str(e)
        }

def check_frontend_files() -> dict:
    """Check if frontend files exist and are accessible"""
    files_to_check = [
        'frontend/index.html',
        'frontend/app.js',
        'frontend/styles.css'
    ]
    
    results = {}
    for file_path in files_to_check:
        results[file_path] = {
            'exists': check_file_exists(file_path),
            'size': Path(file_path).stat().st_size if check_file_exists(file_path) else 0
        }
    
    return results

def check_html_elements() -> dict:
    """Check if required HTML elements exist in the index.html file"""
    if not check_file_exists('frontend/index.html'):
        return {'error': 'index.html not found'}
    
    with open('frontend/index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    required_elements = [
        'id="config-btn"',
        'id="config-overlay"',
        'id="config-close"',
        'id="config-cancel"',
        'id="config-form"',
        'id="target-address-input"'
    ]
    
    results = {}
    for element in required_elements:
        results[element] = element in html_content
    
    return results

def check_javascript_structure() -> dict:
    """Check if required JavaScript methods exist in app.js"""
    if not check_file_exists('frontend/app.js'):
        return {'error': 'app.js not found'}
    
    with open('frontend/app.js', 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    required_methods = [
        'setupConfigPanel()',
        'openConfigPanel()',
        'closeConfigPanel()',
        'new RealEstateMonitor()',
        'addEventListener(\'click\', () => this.openConfigPanel())'
    ]
    
    results = {}
    for method in required_methods:
        results[method] = method in js_content
    
    return results

def start_backend_if_needed() -> bool:
    """Try to start the backend if it's not running"""
    if check_backend_running():
        return True
    
    print("Backend not running, attempting to start...")
    try:
        # Try to start the backend
        subprocess.Popen([sys.executable, 'main.py', '--no-browser'], 
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE)
        
        # Wait a few seconds for it to start
        for i in range(10):
            time.sleep(1)
            if check_backend_running():
                print(f"Backend started successfully after {i+1} seconds")
                return True
        
        print("Backend failed to start within 10 seconds")
        return False
    except Exception as e:
        print(f"Failed to start backend: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("="*60)
    print("  REAL ESTATE MONITOR - SETTINGS BUTTON DIAGNOSTIC")
    print("="*60)
    
    # Check frontend files
    print("\n1. Checking frontend files...")
    frontend_files = check_frontend_files()
    for file_path, info in frontend_files.items():
        status = "✓ EXISTS" if info['exists'] else "✗ MISSING"
        size = f"({info['size']} bytes)" if info['exists'] else ""
        print(f"   {file_path}: {status} {size}")
    
    # Check HTML elements
    print("\n2. Checking HTML elements...")
    html_elements = check_html_elements()
    if 'error' in html_elements:
        print(f"   ✗ ERROR: {html_elements['error']}")
    else:
        for element, exists in html_elements.items():
            status = "✓ FOUND" if exists else "✗ MISSING"
            print(f"   {element}: {status}")
    
    # Check JavaScript structure
    print("\n3. Checking JavaScript structure...")
    js_structure = check_javascript_structure()
    if 'error' in js_structure:
        print(f"   ✗ ERROR: {js_structure['error']}")
    else:
        for method, exists in js_structure.items():
            status = "✓ FOUND" if exists else "✗ MISSING"
            print(f"   {method}: {status}")
    
    # Check backend
    print("\n4. Checking backend status...")
    backend_running = check_backend_running()
    print(f"   Backend running: {'✓ YES' if backend_running else '✗ NO'}")
    
    if not backend_running:
        print("   Attempting to start backend...")
        backend_started = start_backend_if_needed()
        if backend_started:
            backend_running = True
    
    # Test configuration endpoint
    if backend_running:
        print("\n5. Testing configuration endpoint...")
        config_result = check_backend_config_endpoint()
        print(f"   GET /api/config: {'✓ SUCCESS' if config_result['success'] else '✗ FAILED'}")
        if config_result['success']:
            print(f"   Configuration data: {config_result['data']}")
        else:
            print(f"   Error: {config_result['error']}")
    
    # Summary and recommendations
    print("\n" + "="*60)
    print("  DIAGNOSTIC SUMMARY")
    print("="*60)
    
    issues_found = []
    
    # Check for missing files
    for file_path, info in frontend_files.items():
        if not info['exists']:
            issues_found.append(f"Missing file: {file_path}")
    
    # Check for missing HTML elements
    if 'error' not in html_elements:
        for element, exists in html_elements.items():
            if not exists:
                issues_found.append(f"Missing HTML element: {element}")
    
    # Check for missing JavaScript methods
    if 'error' not in js_structure:
        for method, exists in js_structure.items():
            if not exists:
                issues_found.append(f"Missing JavaScript method: {method}")
    
    # Check backend issues
    if not backend_running:
        issues_found.append("Backend server is not running")
    
    if issues_found:
        print("\n✗ ISSUES FOUND:")
        for issue in issues_found:
            print(f"   - {issue}")
        
        print("\n📋 RECOMMENDED ACTIONS:")
        print("   1. Ensure all frontend files exist and are not corrupted")
        print("   2. Start the backend server using: python main.py")
        print("   3. Open browser to http://localhost:5000")
        print("   4. Check browser console for JavaScript errors")
        print("   5. Try clicking the settings button and check for errors")
    else:
        print("\n✓ NO OBVIOUS ISSUES FOUND")
        print("\n📋 NEXT STEPS:")
        print("   1. Open browser to http://localhost:5000")
        print("   2. Open browser developer tools (F12)")
        print("   3. Click the settings button")
        print("   4. Check console for JavaScript errors")
        print("   5. Check if config panel appears (might be hidden by CSS)")
    
    print("\n" + "="*60)

if __name__ == '__main__':
    main()