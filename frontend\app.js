// Real Estate Monitor Frontend JavaScript
// This file handles the interactive functionality for the frontend

class RealEstateMonitor {
    constructor() {
        this.apiBaseUrl = 'http://localhost:5000/api';
        this.socket = null;
        this.isConnected = false;
        this.currentConfig = null;
        this.currentListings = [];
        this.loadingStates = new Set();
        
        // Error handling and retry configuration
        this.maxRetries = 3;
        this.retryDelay = 1000; // Base delay in milliseconds
        this.connectionRetryAttempts = 0;
        this.maxConnectionRetries = 5;
        this.reconnectInterval = null;
        
        this.logger.info('RealEstateMonitor constructor called');
        this.logger.info('Document readyState:', document.readyState);
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            this.logger.info('DOM still loading, waiting for DOMContentLoaded...');
            document.addEventListener('DOMContentLoaded', () => {
                this.logger.info('DOMContentLoaded fired, initializing...');
                this.init();
            });
        } else {
            this.logger.info('DOM already ready, initializing immediately...');
            this.init();
        }
    }

    init() {
        this.setupThemeToggle();
        this.setupConfigPanel();
        this.setupEventListeners();
        this.loadTheme();
        this.initializeWebSocket();
        this.loadInitialData();
    }
    
    async loadInitialData() {
        try {
            this.logger.info('Loading initial application data...');
            
            // Load configuration first
            await this.loadConfig();
            
            // Load status and listings in parallel
            const [statusResult, listingsResult] = await Promise.allSettled([
                this.loadStatus(),
                this.loadListings()
            ]);
            
            // Log any failures but don't throw - app should still work
            if (statusResult.status === 'rejected') {
                this.logger.warn('Failed to load initial status:', statusResult.reason);
            }
            
            if (listingsResult.status === 'rejected') {
                this.logger.warn('Failed to load initial listings:', listingsResult.reason);
            }
            
            this.logger.info('Initial data loading completed');
            
        } catch (error) {
            this.logger.error('Failed to load initial data:', error);
            this.addActivityItem('Failed to load initial data - some features may not work properly');
            
            // Show a non-intrusive error message
            this.showToast('Some data failed to load. Please check your connection.', 'warning');
        }
    }

    // Theme Management
    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        this.showToast(`Switched to ${newTheme} mode`, 'success');
    }

    loadTheme() {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const theme = savedTheme || (prefersDark ? 'dark' : 'light');
        
        document.documentElement.setAttribute('data-theme', theme);
    }

    // Configuration Panel Management
    setupConfigPanel() {
        this.logger.info('Setting up configuration panel...');
        this.logger.info('DOM readyState:', document.readyState);
        this.logger.info('Document body exists:', !!document.body);
        
        // Wait for DOM to be ready if it's not already
        if (document.readyState === 'loading') {
            this.logger.info('DOM still loading, waiting for DOMContentLoaded...');
            document.addEventListener('DOMContentLoaded', () => {
                this.logger.info('DOMContentLoaded fired, retrying setupConfigPanel');
                this._setupConfigPanelElements();
            });
        } else {
            this._setupConfigPanelElements();
        }
    }

    _setupConfigPanelElements() {
        this.logger.info('Setting up config panel elements...');
        
        const configBtn = document.getElementById('config-btn');
        const configOverlay = document.getElementById('config-overlay');
        const configClose = document.getElementById('config-close');
        const configCancel = document.getElementById('config-cancel');

        this.logger.info(`Found elements: btn=${!!configBtn}, overlay=${!!configOverlay}, close=${!!configClose}, cancel=${!!configCancel}`);
        
        // Log element details for debugging
        if (configBtn) {
            this.logger.info('Config button details:', {
                id: configBtn.id,
                className: configBtn.className,
                tagName: configBtn.tagName,
                parentElement: configBtn.parentElement?.tagName
            });
        } else {
            this.logger.error('Config button not found! Available elements with config in ID:');
            const configElements = document.querySelectorAll('[id*="config"]');
            configElements.forEach(el => {
                this.logger.info(`- Found element: ${el.tagName}#${el.id}`);
            });
        }

        if (configBtn) {
            this.logger.info('Adding click listener to config button');
            
            // Remove any existing listeners first
            const newConfigBtn = configBtn.cloneNode(true);
            configBtn.parentNode.replaceChild(newConfigBtn, configBtn);
            
            newConfigBtn.addEventListener('click', (e) => {
                this.logger.info('Config button clicked!', e);
                e.preventDefault();
                e.stopPropagation();
                this.openConfigPanel();
            });
            
            // Test the button immediately
            this.logger.info('Testing config button click handler...');
            setTimeout(() => {
                this.logger.info('Config button test - element still exists:', !!document.getElementById('config-btn'));
            }, 100);
        } else {
            this.logger.error('Config button not found!');
        }

        if (configClose) {
            this.logger.info('Adding click listener to close button');
            configClose.addEventListener('click', () => {
                this.logger.info('Close button clicked');
                this.closeConfigPanel();
            });
        } else {
            this.logger.warn('Config close button not found');
        }

        if (configCancel) {
            this.logger.info('Adding click listener to cancel button');
            configCancel.addEventListener('click', () => {
                this.logger.info('Cancel button clicked');
                this.closeConfigPanel();
            });
        } else {
            this.logger.warn('Config cancel button not found');
        }

        if (configOverlay) {
            this.logger.info('Adding click listener to overlay');
            configOverlay.addEventListener('click', (e) => {
                if (e.target === configOverlay) {
                    this.logger.info('Overlay background clicked');
                    this.closeConfigPanel();
                }
            });
        } else {
            this.logger.warn('Config overlay not found');
        }
        
        this.logger.info('Configuration panel setup completed');
        
        // Add a test method to the global scope for debugging
        window.testConfigPanel = () => {
            this.logger.info('Manual test of config panel triggered');
            this.openConfigPanel();
        };
        
        window.debugConfigElements = () => {
            const btn = document.getElementById('config-btn');
            const overlay = document.getElementById('config-overlay');
            this.logger.info('Debug - Config button:', btn);
            this.logger.info('Debug - Config overlay:', overlay);
            this.logger.info('Debug - All elements with config in ID:', 
                Array.from(document.querySelectorAll('[id*="config"]')).map(el => `${el.tagName}#${el.id}`)
            );
        };
    }

    openConfigPanel() {
        this.logger.info('openConfigPanel() called');
        const configOverlay = document.getElementById('config-overlay');
        if (configOverlay) {
            this.logger.info('Config overlay found, current classes:', configOverlay.className);
            this.logger.info('Current overlay styles:', {
                display: getComputedStyle(configOverlay).display,
                visibility: getComputedStyle(configOverlay).visibility,
                opacity: getComputedStyle(configOverlay).opacity
            });
            
            this.logger.info('Adding active class to overlay');
            configOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Verify the class was added
            this.logger.info('After adding active class:', configOverlay.className);
            this.logger.info('Updated overlay styles:', {
                display: getComputedStyle(configOverlay).display,
                visibility: getComputedStyle(configOverlay).visibility,
                opacity: getComputedStyle(configOverlay).opacity
            });
            
            this.logger.info('Config panel should now be visible');
            
            // Load current configuration into the form
            this.updateConfigUI();
        } else {
            this.logger.error('Config overlay not found in openConfigPanel!');
            // Try to find any element with config-overlay class
            const overlayByClass = document.querySelector('.config-overlay');
            if (overlayByClass) {
                this.logger.info('Found overlay by class instead of ID:', overlayByClass);
            } else {
                this.logger.error('No config overlay found by class either');
            }
        }
    }

    closeConfigPanel() {
        this.logger.info('closeConfigPanel() called');
        const configOverlay = document.getElementById('config-overlay');
        if (configOverlay) {
            this.logger.info('Config overlay found, current classes:', configOverlay.className);
            this.logger.info('Removing active class from overlay');
            configOverlay.classList.remove('active');
            document.body.style.overflow = '';
            
            // Verify the class was removed
            this.logger.info('After removing active class:', configOverlay.className);
            this.logger.info('Config panel should now be hidden');
        } else {
            this.logger.error('Config overlay not found in closeConfigPanel!');
        }
    }

    // Event Listeners Setup
    setupEventListeners() {
        // Status control buttons
        const startBtn = document.getElementById('start-monitoring');
        const stopBtn = document.getElementById('stop-monitoring');
        const checkNowBtn = document.getElementById('check-now');
        const clearActivityBtn = document.getElementById('clear-activity');

        if (startBtn) {
            startBtn.addEventListener('click', () => this.handleStartMonitoring());
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.handleStopMonitoring());
        }

        if (checkNowBtn) {
            checkNowBtn.addEventListener('click', () => this.handleCheckNow());
        }

        if (clearActivityBtn) {
            clearActivityBtn.addEventListener('click', () => this.clearActivityFeed());
        }

        // Configuration form
        const configForm = document.getElementById('config-form');
        if (configForm) {
            configForm.addEventListener('submit', (e) => this.handleConfigSubmit(e));
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    // Keyboard Shortcuts Handler
    handleKeyboardShortcuts(e) {
        // Ctrl+, or Cmd+, to open config panel
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            this.logger.info('Keyboard shortcut triggered: Ctrl+, - opening config panel');
            this.openConfigPanel();
            return;
        }
        
        // Escape to close config panel
        if (e.key === 'Escape') {
            const configOverlay = document.getElementById('config-overlay');
            if (configOverlay && configOverlay.classList.contains('active')) {
                e.preventDefault();
                this.logger.info('Keyboard shortcut triggered: Escape - closing config panel');
                this.closeConfigPanel();
                return;
            }
        }
        
        // Alt+S to open settings (alternative shortcut)
        if (e.altKey && e.key === 's') {
            e.preventDefault();
            this.logger.info('Keyboard shortcut triggered: Alt+S - opening config panel');
            this.openConfigPanel();
            return;
        }
    }

    // API Client Methods with retry logic
    async apiRequest(endpoint, options = {}, retryCount = 0) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: 10000, // 10 second timeout
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            // Create AbortController for timeout handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);
            
            const response = await fetch(url, {
                ...finalOptions,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            // Try to parse JSON response
            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                // If JSON parsing fails, create a generic error response
                data = {
                    success: false,
                    error: response.ok ? 'Invalid JSON response' : `HTTP ${response.status}`
                };
            }
            
            if (!response.ok) {
                const errorMessage = data.error || `HTTP ${response.status}: ${response.statusText}`;
                throw new Error(errorMessage);
            }
            
            return data;
            
        } catch (error) {
            this.logger.error(`API request failed (attempt ${retryCount + 1}): ${error.message}`);
            
            // Determine if we should retry
            const shouldRetry = this._shouldRetryRequest(error, retryCount);
            
            if (shouldRetry) {
                const delay = this._calculateRetryDelay(retryCount);
                this.logger.info(`Retrying API request in ${delay}ms...`);
                
                await this._sleep(delay);
                return this.apiRequest(endpoint, options, retryCount + 1);
            }
            
            // If we've exhausted retries or shouldn't retry, throw the error
            throw error;
        }
    }
    
    _shouldRetryRequest(error, retryCount) {
        // Don't retry if we've exceeded max retries
        if (retryCount >= this.maxRetries) {
            return false;
        }
        
        // Retry on network errors, timeouts, and server errors (5xx)
        if (error.name === 'AbortError' || 
            error.message.includes('fetch') ||
            error.message.includes('network') ||
            error.message.includes('timeout') ||
            error.message.includes('HTTP 5')) {
            return true;
        }
        
        // Don't retry on client errors (4xx) or other specific errors
        return false;
    }
    
    _calculateRetryDelay(retryCount) {
        // Exponential backoff with jitter
        const baseDelay = this.retryDelay * Math.pow(2, retryCount);
        const jitter = Math.random() * 0.3 * baseDelay; // 30% jitter
        return Math.min(baseDelay + jitter, 10000); // Cap at 10 seconds
    }
    
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async loadConfig() {
        try {
            const response = await this.apiRequest('/config');
            this.currentConfig = response.data;
            this.updateConfigUI();
            return response.data;
        } catch (error) {
            this.logger.error('Failed to load configuration:', error);
            this.showToast(`Failed to load configuration: ${error.message}`, 'error');
            
            // Provide graceful degradation - use default config
            this.currentConfig = {
                target_address: '',
                check_interval_minutes: 15,
                notification_enabled: true,
                needs_setup: true
            };
            this.updateConfigUI();
            throw error;
        }
    }

    async saveConfig(configData) {
        try {
            this.setLoading('config-save', true);
            const response = await this.apiRequest('/config', {
                method: 'POST',
                body: JSON.stringify(configData)
            });
            
            this.currentConfig = { ...this.currentConfig, ...configData };
            this.updateConfigUI();
            this.showToast('Configuration saved successfully', 'success');
            return response;
        } catch (error) {
            this.showToast(`Failed to save configuration: ${error.message}`, 'error');
            throw error;
        } finally {
            this.setLoading('config-save', false);
        }
    }

    async loadStatus() {
        try {
            const response = await this.apiRequest('/status');
            this.updateStatusUI(response.data);
            return response.data;
        } catch (error) {
            this.logger.error('Failed to load status:', error);
            this.showToast(`Failed to load status: ${error.message}`, 'error');
            
            // Provide graceful degradation - show offline status
            this.updateStatusUI({
                monitoring_active: false,
                monitoring_status: 'offline',
                last_check_time: null,
                target_address: this.currentConfig?.target_address || '',
                check_interval_minutes: this.currentConfig?.check_interval_minutes || 15,
                current_listings_count: 0,
                found_listings_count: 0,
                needs_setup: true
            });
            throw error;
        }
    }

    async loadListings() {
        try {
            const response = await this.apiRequest('/listings');
            this.currentListings = response.data.listings || [];
            this.updateListingsUI();
            return response.data;
        } catch (error) {
            this.logger.error('Failed to load listings:', error);
            this.showToast(`Failed to load listings: ${error.message}`, 'error');
            
            // Provide graceful degradation - show empty listings
            this.currentListings = [];
            this.updateListingsUI();
            throw error;
        }
    }

    // WebSocket Connection with enhanced error handling and reconnection
    initializeWebSocket() {
        try {
            this.updateConnectionStatus('connecting');
            this.connectionRetryAttempts = 0;
            
            // Clear any existing reconnection interval
            if (this.reconnectInterval) {
                clearInterval(this.reconnectInterval);
                this.reconnectInterval = null;
            }
            
            // Use Socket.IO client library with configuration
            this.socket = io('http://localhost:5000', {
                timeout: 10000,
                reconnection: true,
                reconnectionAttempts: this.maxConnectionRetries,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                maxReconnectionAttempts: this.maxConnectionRetries
            });
            
            this.socket.on('connect', () => {
                this.isConnected = true;
                this.connectionRetryAttempts = 0;
                this.updateConnectionStatus('connected');
                this.addActivityItem('Connected to server');
                this.logger.info('WebSocket connected');
                
                // Clear any reconnection interval
                if (this.reconnectInterval) {
                    clearInterval(this.reconnectInterval);
                    this.reconnectInterval = null;
                }
                
                // Request current status
                this.socket.emit('request_status');
            });
            
            this.socket.on('disconnect', (reason) => {
                this.isConnected = false;
                this.updateConnectionStatus('disconnected');
                this.addActivityItem(`Disconnected from server: ${reason}`);
                this.logger.warn('WebSocket disconnected:', reason);
                
                // Start manual reconnection if automatic reconnection fails
                if (reason === 'io server disconnect' || reason === 'transport close') {
                    this._startManualReconnection();
                }
            });
            
            this.socket.on('connect_error', (error) => {
                this.isConnected = false;
                this.connectionRetryAttempts++;
                
                this.logger.error('WebSocket connection error:', error);
                
                if (this.connectionRetryAttempts <= this.maxConnectionRetries) {
                    this.updateConnectionStatus('connecting');
                    this.addActivityItem(`Connection attempt ${this.connectionRetryAttempts}/${this.maxConnectionRetries} failed, retrying...`);
                } else {
                    this.updateConnectionStatus('error');
                    this.addActivityItem('Failed to connect to server after multiple attempts');
                    this.showToast('Failed to connect to server. Please ensure the backend is running.', 'error');
                    this._startManualReconnection();
                }
            });
            
            this.socket.on('reconnect', (attemptNumber) => {
                this.logger.info(`WebSocket reconnected after ${attemptNumber} attempts`);
                this.addActivityItem(`Reconnected to server (attempt ${attemptNumber})`);
                this.showToast('Reconnected to server', 'success');
            });
            
            this.socket.on('reconnect_error', (error) => {
                this.logger.error('WebSocket reconnection error:', error);
            });
            
            this.socket.on('reconnect_failed', () => {
                this.logger.error('WebSocket reconnection failed');
                this.updateConnectionStatus('error');
                this.addActivityItem('Failed to reconnect to server');
                this.showToast('Failed to reconnect to server', 'error');
                this._startManualReconnection();
            });
            
            this.socket.on('status_update', (data) => {
                this.handleStatusUpdate(data);
            });
            
            this.socket.on('notification', (data) => {
                this.handleNotification(data);
            });
            
            this.socket.on('error', (error) => {
                this.logger.error('WebSocket error:', error);
                this.showToast(`Connection error: ${error.message || 'Unknown error'}`, 'error');
            });
            
        } catch (error) {
            this.logger.error('Failed to initialize WebSocket:', error);
            this.updateConnectionStatus('error');
            this.showToast('Failed to connect to server', 'error');
            this._startManualReconnection();
        }
    }
    
    _startManualReconnection() {
        // Don't start multiple reconnection intervals
        if (this.reconnectInterval) {
            return;
        }
        
        this.logger.info('Starting manual reconnection attempts...');
        
        this.reconnectInterval = setInterval(() => {
            if (!this.isConnected && this.connectionRetryAttempts < this.maxConnectionRetries * 2) {
                this.logger.info('Attempting manual reconnection...');
                this.addActivityItem('Attempting to reconnect...');
                
                try {
                    if (this.socket) {
                        this.socket.disconnect();
                    }
                    this.initializeWebSocket();
                } catch (error) {
                    this.logger.error('Manual reconnection attempt failed:', error);
                }
            } else if (this.isConnected) {
                // Successfully reconnected, clear the interval
                clearInterval(this.reconnectInterval);
                this.reconnectInterval = null;
            } else {
                // Give up after too many attempts
                this.logger.error('Giving up on manual reconnection attempts');
                this.addActivityItem('Unable to reconnect to server. Please refresh the page.');
                clearInterval(this.reconnectInterval);
                this.reconnectInterval = null;
            }
        }, 5000); // Try every 5 seconds
    }

    updateConnectionStatus(status) {
        const connectionStatus = document.getElementById('connection-status');
        if (!connectionStatus) return;

        const statusDot = connectionStatus.querySelector('.status-dot');
        const statusText = connectionStatus.querySelector('.status-text');

        // Remove existing status classes
        connectionStatus.classList.remove('connected', 'disconnected', 'error');
        
        switch (status) {
            case 'connected':
                connectionStatus.classList.add('connected', 'show');
                if (statusText) statusText.textContent = 'Connected';
                // Hide after 3 seconds
                setTimeout(() => {
                    connectionStatus.classList.remove('show');
                }, 3000);
                break;
            case 'disconnected':
                connectionStatus.classList.add('disconnected', 'show');
                if (statusText) statusText.textContent = 'Disconnected';
                break;
            case 'connecting':
                connectionStatus.classList.add('show');
                if (statusText) statusText.textContent = 'Connecting...';
                break;
            case 'error':
                connectionStatus.classList.add('error', 'show');
                if (statusText) statusText.textContent = 'Connection failed';
                break;
        }
    }

    handleStatusUpdate(data) {
        this.logger.info('Status update received:', data);
        this.updateStatusUI(data);
        
        if (data.target_address && this.currentConfig) {
            this.currentConfig.target_address = data.target_address;
            this.updateConfigUI();
        }
    }

    handleNotification(data) {
        this.logger.info('Notification received:', data);
        
        switch (data.notification_type) {
            case 'property_found':
                this.handlePropertyFoundNotification(data);
                break;
            case 'property_removed':
                this.handlePropertyRemovedNotification(data);
                break;
            case 'status_update':
                this.handleStatusNotification(data);
                break;
            case 'error':
                this.handleErrorNotification(data);
                break;
            default:
                this.addActivityItem(data.message || 'Unknown notification received');
        }
    }

    handlePropertyFoundNotification(data) {
        this.showToast(`Property found: ${data.property?.address || 'Unknown address'}`, 'success', 10000);
        this.addActivityItem(`Property found: ${data.property?.address || 'Unknown address'}`);
        
        // Refresh listings to show the new property
        this.loadListings();
    }

    handlePropertyRemovedNotification(data) {
        this.showToast(`Property removed: ${data.address || 'Unknown address'}`, 'warning');
        this.addActivityItem(`Property removed: ${data.address || 'Unknown address'}`);
        
        // Refresh listings to remove the property
        this.loadListings();
    }

    handleStatusNotification(data) {
        const type = data.level === 'error' ? 'error' : data.level === 'warning' ? 'warning' : 'info';
        this.showToast(data.message, type);
        this.addActivityItem(data.message);
    }

    handleErrorNotification(data) {
        this.showToast(`Error: ${data.message}`, 'error');
        this.addActivityItem(`Error: ${data.message}`);
    }

    // Monitoring Control Methods
    async handleStartMonitoring() {
        if (!this.currentConfig?.target_address) {
            this.showToast('Please configure a target address first', 'warning');
            this.openConfigPanel();
            return;
        }

        try {
            this.setLoading('start-monitoring', true);
            await this.apiRequest('/start-monitoring', { method: 'POST' });
            this.showToast('Monitoring started successfully', 'success');
            this.addActivityItem('Monitoring started');
            
            // Refresh status
            await this.loadStatus();
        } catch (error) {
            this.showToast(`Failed to start monitoring: ${error.message}`, 'error');
        } finally {
            this.setLoading('start-monitoring', false);
        }
    }

    async handleStopMonitoring() {
        try {
            this.setLoading('stop-monitoring', true);
            await this.apiRequest('/stop-monitoring', { method: 'POST' });
            this.showToast('Monitoring stopped', 'info');
            this.addActivityItem('Monitoring stopped');
            
            // Refresh status
            await this.loadStatus();
        } catch (error) {
            this.showToast(`Failed to stop monitoring: ${error.message}`, 'error');
        } finally {
            this.setLoading('stop-monitoring', false);
        }
    }

    async handleCheckNow() {
        if (!this.currentConfig?.target_address) {
            this.showToast('Please configure a target address first', 'warning');
            this.openConfigPanel();
            return;
        }

        try {
            this.setLoading('check-now', true);
            const response = await this.apiRequest('/check-now', { method: 'POST' });
            this.showToast(response.message, 'success');
            this.addActivityItem(`Manual check completed - ${response.data.listings_count} listings found`);
            
            // Refresh listings and status
            await Promise.all([this.loadListings(), this.loadStatus()]);
        } catch (error) {
            this.showToast(`Check failed: ${error.message}`, 'error');
        } finally {
            this.setLoading('check-now', false);
        }
    }

    async handleConfigSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const configData = {
            target_address: formData.get('target_address')?.trim(),
            check_interval_minutes: parseInt(formData.get('check_interval_minutes')),
            notification_enabled: formData.get('notifications_enabled') === 'on'
        };

        // Validate form data
        if (!this.validateConfigForm(configData)) {
            return;
        }

        try {
            await this.saveConfig(configData);
            this.closeConfigPanel();
            this.addActivityItem('Configuration updated');
            
            // Refresh status
            await this.loadStatus();
        } catch (error) {
            // Error already shown in saveConfig
        }
    }

    validateConfigForm(configData) {
        const errors = [];
        
        // Clear previous error states
        this.clearFormErrors();

        // Validate target address
        const addressInput = document.getElementById('target-address-input');
        if (!configData.target_address) {
            errors.push('Target address is required');
            this.showFieldError(addressInput, 'Target address is required');
        } else if (configData.target_address.length < 5) {
            errors.push('Target address is too short');
            this.showFieldError(addressInput, 'Address must be at least 5 characters');
        }

        // Validate check interval
        const intervalInput = document.getElementById('check-interval-input');
        if (!configData.check_interval_minutes || configData.check_interval_minutes < 1) {
            errors.push('Check interval must be at least 1 minute');
            this.showFieldError(intervalInput, 'Must be at least 1 minute');
        } else if (configData.check_interval_minutes > 1440) {
            errors.push('Check interval cannot exceed 24 hours (1440 minutes)');
            this.showFieldError(intervalInput, 'Cannot exceed 1440 minutes (24 hours)');
        }

        if (errors.length > 0) {
            this.showToast('Please fix the form errors', 'error');
            return false;
        }

        return true;
    }

    showFieldError(input, message) {
        if (!input) return;
        
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            formGroup.classList.add('error');
            
            // Remove existing error message
            const existingError = formGroup.querySelector('.form-error');
            if (existingError) {
                existingError.remove();
            }
            
            // Add new error message
            const errorEl = document.createElement('span');
            errorEl.className = 'form-error';
            errorEl.textContent = message;
            input.parentNode.insertBefore(errorEl, input.nextSibling);
        }
    }

    clearFormErrors() {
        const errorGroups = document.querySelectorAll('.form-group.error');
        errorGroups.forEach(group => {
            group.classList.remove('error');
            const errorMsg = group.querySelector('.form-error');
            if (errorMsg) {
                errorMsg.remove();
            }
        });
    }

    // Activity Feed Management
    clearActivityFeed() {
        const activityFeed = document.getElementById('activity-feed');
        if (activityFeed) {
            activityFeed.innerHTML = `
                <div class="activity-item">
                    <div class="activity-time">Just now</div>
                    <div class="activity-message">Activity feed cleared</div>
                </div>
            `;
        }
    }

    addActivityItem(message, timestamp = new Date()) {
        const activityFeed = document.getElementById('activity-feed');
        if (!activityFeed) return;

        const timeString = timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <div class="activity-time">${timeString}</div>
            <div class="activity-message">${message}</div>
        `;

        activityFeed.insertBefore(activityItem, activityFeed.firstChild);

        // Keep only the last 50 items
        const items = activityFeed.querySelectorAll('.activity-item');
        if (items.length > 50) {
            items[items.length - 1].remove();
        }
    }

    // Toast Notification System
    showToast(message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 1rem;">
                <div style="flex: 1;">
                    <div style="font-weight: 500; margin-bottom: 0.25rem;">
                        ${this.getToastTitle(type)}
                    </div>
                    <div style="font-size: 0.875rem; color: var(--color-text-secondary);">
                        ${message}
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; color: var(--color-text-muted); cursor: pointer; padding: 0; font-size: 1.25rem;">
                    ×
                </button>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 10);

        // Auto remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    getToastTitle(type) {
        const titles = {
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            info: 'Information'
        };
        return titles[type] || 'Notification';
    }

    // Keyboard Shortcuts
    handleKeyboardShortcuts(e) {
        // Escape key closes config panel
        if (e.key === 'Escape') {
            this.closeConfigPanel();
        }

        // Ctrl/Cmd + K opens config panel
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.openConfigPanel();
        }

        // Ctrl/Cmd + D toggles theme
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            this.toggleTheme();
        }
    }

    // UI Update Methods
    updateConfigUI() {
        if (!this.currentConfig) return;

        const targetAddressInput = document.getElementById('target-address-input');
        const checkIntervalInput = document.getElementById('check-interval-input');
        const notificationsEnabledInput = document.getElementById('notifications-enabled');

        if (targetAddressInput) {
            targetAddressInput.value = this.currentConfig.target_address || '';
        }

        if (checkIntervalInput) {
            checkIntervalInput.value = this.currentConfig.check_interval_minutes || 15;
        }

        if (notificationsEnabledInput) {
            notificationsEnabledInput.checked = this.currentConfig.notification_enabled !== false;
        }

        // Update status details
        this.updateStatusDetails(
            this.currentConfig.target_address,
            this.currentConfig.last_check,
            this.currentConfig.check_interval_minutes || 15
        );
    }

    updateStatusUI(statusData) {
        // Update status indicator
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');

        if (statusDot && statusText) {
            if (statusData.monitoring_active) {
                statusDot.className = 'status-dot running';
                statusText.textContent = 'Running';
            } else {
                statusDot.className = 'status-dot';
                statusText.textContent = 'Stopped';
            }
        }

        // Update status details
        this.updateStatusDetails(
            statusData.target_address,
            statusData.last_check_time,
            statusData.check_interval_minutes || 15
        );

        // Update button states
        const startBtn = document.getElementById('start-monitoring');
        const stopBtn = document.getElementById('stop-monitoring');
        const checkNowBtn = document.getElementById('check-now');

        if (startBtn) {
            startBtn.disabled = statusData.monitoring_active || this.loadingStates.has('start-monitoring');
        }

        if (stopBtn) {
            stopBtn.disabled = !statusData.monitoring_active || this.loadingStates.has('stop-monitoring');
        }

        if (checkNowBtn) {
            checkNowBtn.disabled = this.loadingStates.has('check-now') || !statusData.target_address;
        }

        // Update property count
        if (statusData.found_listings_count !== undefined) {
            this.updatePropertyCount(statusData.found_listings_count);
        }
    }

    updateListingsUI() {
        const propertiesGrid = document.getElementById('properties-grid');
        if (!propertiesGrid) return;

        // Clear existing property cards (but keep empty state)
        const existingCards = propertiesGrid.querySelectorAll('.property-card');
        existingCards.forEach(card => card.remove());

        if (this.currentListings.length === 0) {
            this.showEmptyState();
        } else {
            this.hideEmptyState();
            
            // Create and add property cards
            this.currentListings.forEach(listing => {
                const card = this.createPropertyCard(listing);
                if (card) {
                    propertiesGrid.appendChild(card);
                }
            });
        }

        this.updatePropertyCount(this.currentListings.length);
    }

    // Property Card Management
    createPropertyCard(property) {
        const template = document.getElementById('property-card-template');
        if (!template) {
            this.logger.error('Property card template not found');
            return null;
        }

        const card = template.content.cloneNode(true);
        
        // Populate card with property data
        const addressEl = card.querySelector('.property-address');
        const priceEl = card.querySelector('.property-price');
        const bedroomsEl = card.querySelector('.bedrooms');
        const bathroomsEl = card.querySelector('.bathrooms');
        const squareFootageEl = card.querySelector('.square-footage');
        const mlsNumberEl = card.querySelector('.mls-number');
        const descriptionEl = card.querySelector('.property-description');
        const linkEl = card.querySelector('.property-link');
        const timestampEl = card.querySelector('.timestamp');
        const imageEl = card.querySelector('.property-image img');

        if (addressEl) addressEl.textContent = property.address || 'Unknown Address';
        if (priceEl) priceEl.textContent = property.price || 'Price not available';
        if (bedroomsEl) bedroomsEl.textContent = property.bedrooms || 'N/A';
        if (bathroomsEl) bathroomsEl.textContent = property.bathrooms || 'N/A';
        if (squareFootageEl) squareFootageEl.textContent = property.square_footage || 'N/A';
        if (mlsNumberEl) mlsNumberEl.textContent = property.mls_number || 'N/A';
        if (descriptionEl) descriptionEl.textContent = property.description || 'No description available';
        if (timestampEl) {
            const date = property.first_seen ? new Date(property.first_seen) : new Date();
            timestampEl.textContent = date.toLocaleString();
        }

        if (linkEl) {
            if (property.listing_url) {
                linkEl.href = property.listing_url;
                linkEl.style.display = 'inline-flex';
            } else {
                linkEl.style.display = 'none';
            }
        }

        if (imageEl && property.image_url) {
            imageEl.src = property.image_url;
            imageEl.alt = `Property at ${property.address}`;
            imageEl.onerror = () => {
                // Hide image container if image fails to load
                const imageContainer = card.querySelector('.property-image');
                if (imageContainer) {
                    imageContainer.style.display = 'none';
                }
            };
        } else if (imageEl) {
            // Hide image container if no image URL
            const imageContainer = card.querySelector('.property-image');
            if (imageContainer) {
                imageContainer.style.display = 'none';
            }
        }

        return card;
    }

    updatePropertyCount(count) {
        const propertyCount = document.getElementById('property-count');
        if (propertyCount) {
            propertyCount.textContent = count;
        }
    }

    showEmptyState() {
        const emptyState = document.getElementById('empty-state');
        if (emptyState) {
            emptyState.style.display = 'block';
        }
    }

    hideEmptyState() {
        const emptyState = document.getElementById('empty-state');
        if (emptyState) {
            emptyState.style.display = 'none';
        }
    }

    // Status Management
    updateStatusDetails(targetAddress, lastCheck, checkInterval) {
        const targetAddressEl = document.getElementById('target-address');
        const lastCheckEl = document.getElementById('last-check');
        const checkIntervalEl = document.getElementById('check-interval');

        if (targetAddressEl) {
            targetAddressEl.textContent = targetAddress || 'Not configured';
        }

        if (lastCheckEl) {
            if (lastCheck) {
                const date = new Date(lastCheck);
                lastCheckEl.textContent = date.toLocaleString();
            } else {
                lastCheckEl.textContent = 'Never';
            }
        }

        if (checkIntervalEl) {
            checkIntervalEl.textContent = `${checkInterval || 15} minutes`;
        }
    }

    // Loading State Management
    setLoading(elementId, isLoading) {
        if (isLoading) {
            this.loadingStates.add(elementId);
        } else {
            this.loadingStates.delete(elementId);
        }

        const element = document.getElementById(elementId);
        if (element) {
            element.disabled = isLoading;
            
            // Add loading class and text
            if (isLoading) {
                element.classList.add('loading');
                element.dataset.originalText = element.textContent;
                
                // Set loading text based on element
                switch (elementId) {
                    case 'start-monitoring':
                        element.textContent = 'Starting...';
                        break;
                    case 'stop-monitoring':
                        element.textContent = 'Stopping...';
                        break;
                    case 'check-now':
                        element.textContent = 'Checking...';
                        break;
                    case 'config-save':
                        element.textContent = 'Saving...';
                        break;
                    default:
                        element.textContent = 'Loading...';
                }
            } else {
                element.classList.remove('loading');
                if (element.dataset.originalText) {
                    element.textContent = element.dataset.originalText;
                    delete element.dataset.originalText;
                }
            }
        }

        // Update other button states based on current status
        this.updateButtonStates();
    }

    updateButtonStates() {
        const startBtn = document.getElementById('start-monitoring');
        const stopBtn = document.getElementById('stop-monitoring');
        const checkNowBtn = document.getElementById('check-now');

        // These will be properly updated when status is loaded
        // This is just to handle loading states
        if (startBtn) {
            startBtn.disabled = this.loadingStates.has('start-monitoring');
        }
        if (stopBtn) {
            stopBtn.disabled = this.loadingStates.has('stop-monitoring');
        }
        if (checkNowBtn) {
            checkNowBtn.disabled = this.loadingStates.has('check-now');
        }
    }

    // Data Loading
    async loadInitialData() {
        try {
            // Load configuration and status in parallel
            await Promise.all([
                this.loadConfig().catch(err => this.logger.error('Failed to load config:', err)),
                this.loadStatus().catch(err => this.logger.error('Failed to load status:', err)),
                this.loadListings().catch(err => this.logger.error('Failed to load listings:', err))
            ]);
        } catch (error) {
            this.logger.error('Failed to load initial data:', error);
        }
    }

    // Logger utility
    get logger() {
        return {
            info: (...args) => console.log('[RealEstateMonitor]', ...args),
            warn: (...args) => console.warn('[RealEstateMonitor]', ...args),
            error: (...args) => console.error('[RealEstateMonitor]', ...args),
            debug: (...args) => console.debug('[RealEstateMonitor]', ...args)
        };
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.realEstateMonitor = new RealEstateMonitor();
    
    // Add initial activity log
    window.realEstateMonitor.addActivityItem('Application initialized');
    
    // Show welcome message
    setTimeout(() => {
        window.realEstateMonitor.showToast(
            'Welcome to Real Estate Monitor! Configure your target address to get started.',
            'info',
            8000
        );
    }, 1000);
});

// Handle system theme changes
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
        const theme = e.matches ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', theme);
    }
});