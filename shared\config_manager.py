"""
Configuration management for the Real Estate Monitor application.
"""
import json
import os
from pathlib import Path
from typing import Op<PERSON>
from datetime import datetime

from .models import Config
from .data_persistence import DataPersistenceManager


class ConfigManager:
    """Manages application configuration loading, saving, and validation"""
    
    DEFAULT_CONFIG_FILE = "config.json"
    DEFAULT_DATA_DIR = "data"
    
    def __init__(self, config_file: Optional[str] = None, data_dir: Optional[str] = None):
        """Initialize ConfigManager with optional custom paths"""
        self.data_dir = Path(data_dir or self.DEFAULT_DATA_DIR)
        self.config_file = self.data_dir / (config_file or self.DEFAULT_CONFIG_FILE)
        
        # Initialize data persistence manager
        self.persistence_manager = DataPersistenceManager(str(self.data_dir))
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
    
    def load_config(self) -> Config:
        """
        Load configuration from file.
        Creates default config if file doesn't exist.
        
        Returns:
            Config: The loaded or default configuration
            
        Raises:
            ValueError: If config file contains invalid data
            IOError: If config file cannot be read
        """
        if not self.config_file.exists():
            return self._create_default_config()
        
        try:
            # Use persistence manager for safe reading with corruption handling
            data = self.persistence_manager._safe_read_json(self.config_file)
            
            # Apply data migration if needed
            data = self.persistence_manager.migrate_config(data)
            
            # Validate that required fields exist
            if 'target_address' not in data:
                raise ValueError("Configuration missing required 'target_address' field")
            
            return Config.from_dict(data)
            
        except FileNotFoundError:
            return self._create_default_config()
        except Exception as e:
            if "corrupted" in str(e).lower():
                # If config is corrupted, create a new default config
                self.persistence_manager.logger.warning(f"Config file corrupted, creating new default: {e}")
                return self._create_default_config()
            raise ValueError(f"Error loading configuration: {e}")
    
    def save_config(self, config: Config) -> None:
        """
        Save configuration to file.
        
        Args:
            config: Configuration object to save
            
        Raises:
            IOError: If config file cannot be written
            ValueError: If config object is invalid
        """
        if not isinstance(config, Config):
            raise ValueError("config must be a Config instance")
        
        try:
            # Ensure data directory exists
            self.data_dir.mkdir(exist_ok=True)
            
            # Convert config to dictionary and add schema version
            config_data = config.to_dict()
            config_data["schema_version"] = self.persistence_manager.CURRENT_SCHEMA_VERSION
            
            # Use persistence manager for safe writing with backup
            self.persistence_manager._safe_write_json(self.config_file, config_data)
                
        except Exception as e:
            raise IOError(f"Error saving configuration: {e}")
    
    def get_target_address(self) -> str:
        """Get the currently configured target address"""
        config = self.load_config()
        return config.target_address
    
    def set_target_address(self, address: str) -> None:
        """
        Set the target address and save configuration.
        
        Args:
            address: The new target address to monitor
            
        Raises:
            ValueError: If address format is invalid
        """
        if not address or not address.strip():
            raise ValueError("Address cannot be empty")
        
        # Load current config and update target address
        config = self.load_config()
        config.target_address = address.strip()
        
        # This will trigger validation in Config.__post_init__
        # Save the updated config
        self.save_config(config)
    
    def get_check_interval(self) -> int:
        """Get the current check interval in minutes"""
        config = self.load_config()
        return config.check_interval_minutes
    
    def set_check_interval(self, minutes: int) -> None:
        """
        Set the check interval and save configuration.
        
        Args:
            minutes: Check interval in minutes (must be positive)
            
        Raises:
            ValueError: If interval is not positive
        """
        if minutes <= 0:
            raise ValueError("Check interval must be positive")
        
        config = self.load_config()
        config.check_interval_minutes = minutes
        self.save_config(config)
    
    def update_last_check(self, timestamp: Optional[datetime] = None) -> None:
        """
        Update the last check timestamp.
        
        Args:
            timestamp: The timestamp to set (defaults to current time)
        """
        config = self.load_config()
        config.last_check = timestamp or datetime.now()
        self.save_config(config)
    
    def add_found_listing(self, mls_number: str) -> None:
        """
        Add a found listing to the tracking list.
        
        Args:
            mls_number: MLS number of the found listing
        """
        if not mls_number or not mls_number.strip():
            raise ValueError("MLS number cannot be empty")
        
        config = self.load_config()
        mls_number = mls_number.strip()
        
        if mls_number not in config.found_listings:
            config.found_listings.append(mls_number)
            self.save_config(config)
    
    def remove_found_listing(self, mls_number: str) -> None:
        """
        Remove a listing from the tracking list.
        
        Args:
            mls_number: MLS number of the listing to remove
        """
        if not mls_number or not mls_number.strip():
            raise ValueError("MLS number cannot be empty")
        
        config = self.load_config()
        mls_number = mls_number.strip()
        
        if mls_number in config.found_listings:
            config.found_listings.remove(mls_number)
            self.save_config(config)
    
    def is_first_run(self) -> bool:
        """Check if this is the first run (no config file exists)"""
        return not self.config_file.exists()
    
    def needs_address_setup(self) -> bool:
        """Check if the target address needs to be configured (still using placeholder)"""
        try:
            config = self.load_config()
            return config.target_address == "1 PLACEHOLDER STREET"
        except Exception:
            return True
    
    def _create_default_config(self) -> Config:
        """
        Create a default configuration.
        Uses a placeholder address that passes validation but indicates setup is needed.
        """
        return Config(
            target_address="1 PLACEHOLDER STREET",  # Placeholder that passes validation
            check_interval_minutes=15,
            notification_enabled=True,
            last_check=None,
            found_listings=[]
        )
    
    def validate_address_format(self, address: str) -> bool:
        """
        Validate address format using the same logic as Config class.
        
        Args:
            address: Address string to validate
            
        Returns:
            bool: True if address format is valid
        """
        if not address or not address.strip():
            return False
        
        # Use the same validation logic as Config class
        import re
        pattern = r'^\d+.*[a-zA-Z].*'
        return bool(re.match(pattern, address.strip()))
    
    def get_config_file_path(self) -> Path:
        """Get the path to the configuration file"""
        return self.config_file
    
    def get_data_dir_path(self) -> Path:
        """Get the path to the data directory"""
        return self.data_dir