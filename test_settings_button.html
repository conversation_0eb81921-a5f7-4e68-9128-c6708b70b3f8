<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .config-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .config-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .config-panel {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            max-width: 400px;
            height: 100%;
            background-color: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            padding: 20px;
        }
        .config-overlay.active .config-panel {
            transform: translateX(0);
        }
        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Settings Button Test</h1>
    
    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="btn" id="config-btn">Settings (Test Button)</button>
        <button class="btn btn-secondary" onclick="clearLog()">Clear Log</button>
        <button class="btn btn-secondary" onclick="testDirectOpen()">Direct Open Test</button>
    </div>
    
    <div class="test-section">
        <h2>Event Log</h2>
        <div id="log" class="log">Test started...\n</div>
    </div>
    
    <!-- Configuration Panel -->
    <div class="config-overlay" id="config-overlay">
        <div class="config-panel">
            <h2>Configuration Test Panel</h2>
            <form id="config-form">
                <div class="form-group">
                    <label for="target-address-input">Target Address</label>
                    <input type="text" id="target-address-input" name="target_address" placeholder="e.g., 123 Main Street, City">
                </div>
                <div class="form-group">
                    <label for="check-interval-input">Check Interval (minutes)</label>
                    <input type="number" id="check-interval-input" name="check_interval_minutes" min="1" max="1440" value="15">
                </div>
                <div style="margin-top: 20px;">
                    <button type="button" class="btn" id="config-save">Save Configuration</button>
                    <button type="button" class="btn btn-secondary" id="config-cancel">Cancel</button>
                    <button type="button" class="btn btn-secondary" id="config-close">Close (X)</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Simple logging function
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = 'Log cleared...\n';
        }
        
        // Test class similar to RealEstateMonitor
        class SettingsButtonTest {
            constructor() {
                log('SettingsButtonTest constructor called');
                this.init();
            }
            
            init() {
                log('Initializing settings button test...');
                this.setupConfigPanel();
            }
            
            setupConfigPanel() {
                log('Setting up config panel...');
                
                const configBtn = document.getElementById('config-btn');
                const configOverlay = document.getElementById('config-overlay');
                const configClose = document.getElementById('config-close');
                const configCancel = document.getElementById('config-cancel');
                
                log(`Found elements: btn=${!!configBtn}, overlay=${!!configOverlay}, close=${!!configClose}, cancel=${!!configCancel}`);
                
                if (configBtn) {
                    log('Adding click listener to config button');
                    configBtn.addEventListener('click', () => {
                        log('Config button clicked!');
                        this.openConfigPanel();
                    });
                } else {
                    log('ERROR: Config button not found!');
                }
                
                if (configClose) {
                    configClose.addEventListener('click', () => {
                        log('Close button clicked');
                        this.closeConfigPanel();
                    });
                }
                
                if (configCancel) {
                    configCancel.addEventListener('click', () => {
                        log('Cancel button clicked');
                        this.closeConfigPanel();
                    });
                }
                
                if (configOverlay) {
                    configOverlay.addEventListener('click', (e) => {
                        if (e.target === configOverlay) {
                            log('Overlay background clicked');
                            this.closeConfigPanel();
                        }
                    });
                }
            }
            
            openConfigPanel() {
                log('openConfigPanel() called');
                const configOverlay = document.getElementById('config-overlay');
                if (configOverlay) {
                    log('Adding active class to overlay');
                    configOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    log('Config panel should now be visible');
                } else {
                    log('ERROR: Config overlay not found in openConfigPanel!');
                }
            }
            
            closeConfigPanel() {
                log('closeConfigPanel() called');
                const configOverlay = document.getElementById('config-overlay');
                if (configOverlay) {
                    log('Removing active class from overlay');
                    configOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    log('Config panel should now be hidden');
                } else {
                    log('ERROR: Config overlay not found in closeConfigPanel!');
                }
            }
        }
        
        // Test function to directly open panel
        function testDirectOpen() {
            log('Direct open test triggered');
            const overlay = document.getElementById('config-overlay');
            if (overlay) {
                overlay.classList.add('active');
                log('Direct open: active class added');
            } else {
                log('Direct open: overlay not found');
            }
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            log('DOM loaded, creating test instance');
            window.settingsTest = new SettingsButtonTest();
        });
        
        // Additional debugging
        window.addEventListener('load', () => {
            log('Window loaded');
            
            // Check if elements exist
            const elements = ['config-btn', 'config-overlay', 'config-close', 'config-cancel'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                log(`Element check - ${id}: ${element ? 'EXISTS' : 'MISSING'}`);
            });
        });
    </script>
</body>
</html>