"""
Unit tests for the MeadowNorthScraper class.
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import requests
from datetime import datetime
from bs4 import BeautifulSoup

from backend.scraper import MeadowNorthScraper
from shared.models import PropertyListing


class TestMeadowNorthScraper(unittest.TestCase):
    """Test cases for MeadowNorthScraper"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.scraper = MeadowNorthScraper()
        
        # Mock HTML content for testing
        self.mock_html = """
        <html>
            <body>
                <div class="listing-card">
                    <div class="price">$450,000</div>
                    <div class="address">123 Main Street</div>
                    <div class="mls">MLS# 12345</div>
                    <div class="description">Beautiful 3 bedroom home with modern updates</div>
                    <div class="details">3 bed, 2 bath, 1,500 sq ft</div>
                    <div class="lot">0.25 acres</div>
                    <div class="year">Built 2010</div>
                    <img src="/images/property1.jpg" alt="Property image">
                    <a href="/listing/12345">View Details</a>
                </div>
                <div class="listing-card">
                    <div class="price">$325,000</div>
                    <div class="address">456 Oak Avenue</div>
                    <div class="mls">MLS# 67890</div>
                    <div class="description">Cozy starter home in quiet neighborhood</div>
                    <div class="details">2 bed, 1 bath, 1,200 sq ft</div>
                    <img src="https://example.com/image2.jpg" alt="Property image">
                    <a href="https://meadownorth.ca/listing/67890">View Details</a>
                </div>
            </body>
        </html>
        """
        
        # Minimal HTML for edge case testing
        self.minimal_html = """
        <html>
            <body>
                <div class="property">
                    <span>$200,000</span>
                    <span>789 Pine Road</span>
                    <span>#ABC123</span>
                </div>
            </body>
        </html>
        """
        
        # Invalid HTML for error testing
        self.invalid_html = """
        <html>
            <body>
                <div class="not-a-listing">
                    <span>Not a price</span>
                    <span>Not an address</span>
                </div>
            </body>
        </html>
        """
    
    def test_init(self):
        """Test scraper initialization"""
        scraper = MeadowNorthScraper()
        self.assertEqual(scraper.base_url, "https://meadownorth.ca")
        self.assertEqual(scraper.listings_url, "https://meadownorth.ca/listings.html")
        self.assertIsInstance(scraper.session, requests.Session)
        self.assertEqual(scraper.min_request_interval, 1.0)
    
    def test_init_custom_url(self):
        """Test scraper initialization with custom URL"""
        custom_url = "https://test.example.com"
        scraper = MeadowNorthScraper(base_url=custom_url)
        self.assertEqual(scraper.base_url, custom_url)
        self.assertEqual(scraper.listings_url, f"{custom_url}/listings.html")
    
    @patch('backend.scraper.time.sleep')
    @patch('backend.scraper.time.time')
    def test_rate_limit(self, mock_time, mock_sleep):
        """Test rate limiting functionality"""
        # Mock time progression: current_time, then final time.time() call
        mock_time.side_effect = [0.5, 1.5]  # current time, then final update
        
        self.scraper.last_request_time = 0
        self.scraper._rate_limit()
        
        # Should sleep for 0.5 seconds (1.0 - 0.5)
        mock_sleep.assert_called_once_with(0.5)
    
    @patch('backend.scraper.time.sleep')
    @patch('backend.scraper.time.time')
    def test_rate_limit_no_sleep_needed(self, mock_time, mock_sleep):
        """Test rate limiting when no sleep is needed"""
        # Mock time progression - enough time has passed
        mock_time.side_effect = [2.0, 2.0]  # current time, then final update
        
        self.scraper.last_request_time = 0
        self.scraper._rate_limit()
        
        # Should not sleep
        mock_sleep.assert_not_called()
    
    @patch('backend.scraper.MeadowNorthScraper.parse_listing_html')
    @patch('requests.Session.get')
    def test_fetch_listings_success(self, mock_get, mock_parse):
        """Test successful listing fetch"""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.text = self.mock_html
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock parse method
        expected_listings = [
            PropertyListing(
                address="123 Main Street",
                price="$450,000",
                mls_number="12345",
                description="Test listing"
            )
        ]
        mock_parse.return_value = expected_listings
        
        result = self.scraper.fetch_listings()
        
        mock_get.assert_called_once_with("https://meadownorth.ca/listings.html", timeout=30)
        mock_parse.assert_called_once_with(self.mock_html)
        self.assertEqual(result, expected_listings)
    
    @patch('requests.Session.get')
    def test_fetch_listings_timeout(self, mock_get):
        """Test fetch listings with timeout error"""
        mock_get.side_effect = requests.exceptions.Timeout()
        
        with self.assertRaises(requests.RequestException) as context:
            self.scraper.fetch_listings()
        
        self.assertIn("Request timed out", str(context.exception))
    
    @patch('requests.Session.get')
    def test_fetch_listings_connection_error(self, mock_get):
        """Test fetch listings with connection error"""
        mock_get.side_effect = requests.exceptions.ConnectionError()
        
        with self.assertRaises(requests.RequestException) as context:
            self.scraper.fetch_listings()
        
        self.assertIn("Connection error", str(context.exception))
    
    @patch('requests.Session.get')
    def test_fetch_listings_http_error(self, mock_get):
        """Test fetch listings with HTTP error"""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        mock_get.return_value.raise_for_status.side_effect = requests.exceptions.HTTPError(response=mock_response)
        
        with self.assertRaises(requests.RequestException) as context:
            self.scraper.fetch_listings()
        
        self.assertIn("HTTP error: 404", str(context.exception))
    
    def test_parse_listing_html_success(self):
        """Test successful HTML parsing"""
        listings = self.scraper.parse_listing_html(self.mock_html)
        
        self.assertEqual(len(listings), 2)
        
        # Check first listing
        listing1 = listings[0]
        self.assertEqual(listing1.address, "123 Main Street")
        self.assertEqual(listing1.price, "$450,000")
        self.assertEqual(listing1.mls_number, "12345")
        self.assertIn("Beautiful 3 bedroom", listing1.description)
        self.assertEqual(listing1.bedrooms, 3)
        self.assertEqual(listing1.bathrooms, 2)
        self.assertEqual(listing1.square_footage, "1,500")
        self.assertEqual(listing1.lot_size, "0.25")
        self.assertEqual(listing1.year_built, "2010")
        self.assertEqual(listing1.image_url, "https://meadownorth.ca/images/property1.jpg")
        self.assertEqual(listing1.listing_url, "https://meadownorth.ca/listing/12345")
        
        # Check second listing
        listing2 = listings[1]
        self.assertEqual(listing2.address, "456 Oak Avenue")
        self.assertEqual(listing2.price, "$325,000")
        self.assertEqual(listing2.mls_number, "67890")
        self.assertEqual(listing2.bedrooms, 2)
        self.assertEqual(listing2.bathrooms, 1)
        self.assertEqual(listing2.image_url, "https://example.com/image2.jpg")
        self.assertEqual(listing2.listing_url, "https://meadownorth.ca/listing/67890")
    
    def test_parse_listing_html_minimal(self):
        """Test HTML parsing with minimal data"""
        listings = self.scraper.parse_listing_html(self.minimal_html)
        
        self.assertEqual(len(listings), 1)
        listing = listings[0]
        self.assertEqual(listing.address, "789 Pine Road")
        self.assertEqual(listing.price, "$200,000")
        self.assertEqual(listing.mls_number, "ABC123")
    
    def test_parse_listing_html_no_listings(self):
        """Test HTML parsing with no valid listings"""
        listings = self.scraper.parse_listing_html(self.invalid_html)
        self.assertEqual(len(listings), 0)
    
    def test_parse_listing_html_invalid(self):
        """Test HTML parsing with malformed HTML"""
        # BeautifulSoup is very forgiving, so we need truly malformed HTML to trigger an error
        malformed_html = None  # This will cause an actual parsing error
        with self.assertRaises(ValueError):
            self.scraper.parse_listing_html(malformed_html)
    
    def test_extract_property_data_complete(self):
        """Test property data extraction with complete data"""
        soup = BeautifulSoup(self.mock_html, 'html.parser')
        element = soup.find('div', class_='listing-card')
        
        listing = self.scraper.extract_property_data(element)
        
        self.assertIsNotNone(listing)
        self.assertEqual(listing.address, "123 Main Street")
        self.assertEqual(listing.price, "$450,000")
        self.assertEqual(listing.mls_number, "12345")
        self.assertEqual(listing.bedrooms, 3)
        self.assertEqual(listing.bathrooms, 2)
        self.assertEqual(listing.square_footage, "1,500")
        self.assertEqual(listing.lot_size, "0.25")
        self.assertEqual(listing.year_built, "2010")
    
    def test_extract_property_data_missing_required(self):
        """Test property data extraction with missing required fields"""
        html = "<div><span>Not a valid listing</span></div>"
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        listing = self.scraper.extract_property_data(element)
        self.assertIsNone(listing)
    
    def test_extract_price_patterns(self):
        """Test price extraction with various patterns"""
        test_cases = [
            ("<div>$450,000</div>", "$450,000"),
            ("<div>Price: $325,000</div>", "$325,000"),
            ("<div>450000 dollars</div>", "$450000"),
            ("<div>Price $1,250,000</div>", "$1,250,000"),
            ("<div class='price'>$199,999</div>", "$199,999"),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_price(element)
                self.assertEqual(result, expected)
    
    def test_extract_address_patterns(self):
        """Test address extraction with various patterns"""
        test_cases = [
            ("<div>123 Main Street</div>", "123 Main Street"),
            ("<div>456 Oak Avenue</div>", "456 Oak Avenue"),
            ("<div>789 Pine Road</div>", "789 Pine Road"),
            ("<div>1001 Elm Drive</div>", "1001 Elm Drive"),
            ("<div class='address'>555 Maple Lane</div>", "555 Maple Lane"),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_address(element)
                self.assertEqual(result, expected)
    
    def test_extract_mls_patterns(self):
        """Test MLS number extraction with various patterns"""
        test_cases = [
            ("<div>MLS# 12345</div>", "12345"),
            ("<div>MLS: ABC123</div>", "ABC123"),
            ("<div>MLS 67890</div>", "67890"),
            ("<div>#XYZ789</div>", "XYZ789"),
            ("<div class='mls'>MLS# 456</div>", "456"),  # Fixed: need MLS pattern in class element
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_mls_number(element)
                self.assertEqual(result, expected)
    
    def test_extract_bedrooms_patterns(self):
        """Test bedroom extraction with various patterns"""
        test_cases = [
            ("<div>3 bed</div>", 3),
            ("<div>2 bedroom</div>", 2),
            ("<div>4 br</div>", 4),
            ("<div>1 bd</div>", 1),
            ("<div>5 bedrooms</div>", 5),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_bedrooms(element)
                self.assertEqual(result, expected)
    
    def test_extract_bathrooms_patterns(self):
        """Test bathroom extraction with various patterns"""
        test_cases = [
            ("<div>2 bath</div>", 2),
            ("<div>1.5 bathroom</div>", 1),
            ("<div>3 ba</div>", 3),
            ("<div>2.5 bathrooms</div>", 2),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_bathrooms(element)
                self.assertEqual(result, expected)
    
    def test_extract_square_footage_patterns(self):
        """Test square footage extraction with various patterns"""
        test_cases = [
            ("<div>1,500 sq ft</div>", "1,500"),
            ("<div>2000 square feet</div>", "2000"),
            ("<div>1200 sf</div>", "1200"),
            ("<div>3,000 sq. ft.</div>", "3,000"),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_square_footage(element)
                self.assertEqual(result, expected)
    
    def test_extract_lot_size_patterns(self):
        """Test lot size extraction with various patterns"""
        test_cases = [
            ("<div>0.25 acres</div>", "0.25"),
            ("<div>1.5 acre</div>", "1.5"),
            ("<div>10000 sq ft lot</div>", "10000"),
            ("<div>2.3 ac</div>", "2.3"),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_lot_size(element)
                self.assertEqual(result, expected)
    
    def test_extract_year_built_patterns(self):
        """Test year built extraction with various patterns"""
        test_cases = [
            ("<div>Built 2010</div>", "2010"),
            ("<div>Year: 1995</div>", "1995"),
            ("<div>2020 built</div>", "2020"),
            ("<div>Built in 1985</div>", "1985"),
        ]
        
        for html, expected in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_year_built(element)
                self.assertEqual(result, expected)
    
    def test_extract_year_built_invalid(self):
        """Test year built extraction with invalid years"""
        test_cases = [
            "<div>Built 1700</div>",  # Too old
            "<div>Built 2030</div>",  # Future year
            "<div>Built 99</div>",    # Too short
        ]
        
        for html in test_cases:
            with self.subTest(html=html):
                soup = BeautifulSoup(html, 'html.parser')
                element = soup.find('div')
                result = self.scraper._extract_year_built(element)
                self.assertIsNone(result)
    
    def test_extract_image_url_relative(self):
        """Test image URL extraction with relative path"""
        html = '<div><img src="/images/property.jpg" alt="Property"></div>'
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        result = self.scraper._extract_image_url(element)
        self.assertEqual(result, "https://meadownorth.ca/images/property.jpg")
    
    def test_extract_image_url_absolute(self):
        """Test image URL extraction with absolute URL"""
        html = '<div><img src="https://example.com/image.jpg" alt="Property"></div>'
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        result = self.scraper._extract_image_url(element)
        self.assertEqual(result, "https://example.com/image.jpg")
    
    def test_extract_image_url_none(self):
        """Test image URL extraction with no image"""
        html = '<div>No image here</div>'
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        result = self.scraper._extract_image_url(element)
        self.assertEqual(result, "")
    
    def test_extract_listing_url_relative(self):
        """Test listing URL extraction with relative path"""
        html = '<div><a href="/listing/12345">View Details</a></div>'
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        result = self.scraper._extract_listing_url(element)
        self.assertEqual(result, "https://meadownorth.ca/listing/12345")
    
    def test_extract_listing_url_absolute(self):
        """Test listing URL extraction with absolute URL"""
        html = '<div><a href="https://meadownorth.ca/listing/67890">View Details</a></div>'
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        result = self.scraper._extract_listing_url(element)
        self.assertEqual(result, "https://meadownorth.ca/listing/67890")
    
    def test_extract_listing_url_none(self):
        """Test listing URL extraction with no link"""
        html = '<div>No link here</div>'
        soup = BeautifulSoup(html, 'html.parser')
        element = soup.find('div')
        
        result = self.scraper._extract_listing_url(element)
        self.assertEqual(result, "")


if __name__ == '__main__':
    unittest.main()