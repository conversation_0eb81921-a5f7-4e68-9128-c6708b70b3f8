/* CSS Custom Properties for Theme System */
:root {
  /* Color Palette */
  --color-primary: #0D7377;
  --color-secondary: #F4A460;
  --color-success: #10B981;
  --color-alert: #F59E0B;
  --color-error: #EF4444;
  --color-background: #FAFAF9;
  --color-surface: #FFFFFF;
  --color-text-primary: #1F2937;
  --color-text-secondary: #6B7280;
  --color-text-muted: #9CA3AF;
  --color-border: #E5E7EB;
  --color-border-light: #F3F4F6;
  
  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark Mode Theme */
[data-theme="dark"] {
  --color-background: #1A1A1A;
  --color-surface: #2D2D2D;
  --color-text-primary: #F9FAFB;
  --color-text-secondary: #D1D5DB;
  --color-text-muted: #9CA3AF;
  --color-border: #374151;
  --color-border-light: #4B5563;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}

/* Header */
.header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background-color: var(--color-surface);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.theme-toggle:hover {
  background-color: var(--color-border-light);
  border-color: var(--color-text-muted);
}

.theme-toggle .icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: opacity var(--transition-fast);
}

.theme-toggle .moon-icon {
  position: absolute;
  opacity: 0;
}

[data-theme="dark"] .theme-toggle .sun-icon {
  opacity: 0;
}

[data-theme="dark"] .theme-toggle .moon-icon {
  opacity: 1;
}

/* Main Content */
.main {
  padding: var(--spacing-xl) 0;
  min-height: calc(100vh - 80px);
}

/* Section Styles */
section {
  margin-bottom: var(--spacing-2xl);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

/* Status Section */
.status-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.status-header h2 {
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: var(--color-text-muted);
  transition: background-color var(--transition-fast);
}

.status-dot.running {
  background-color: var(--color-success);
  animation: pulse 2s infinite;
}

.status-dot.error {
  background-color: var(--color-error);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-weight: 500;
  color: var(--color-text-secondary);
}

.status-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.status-item .label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-item .value {
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  color: var(--color-text-primary);
  word-break: break-all;
}

.status-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* Properties Section */
.property-count {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  background-color: var(--color-border-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

@media (max-width: 768px) {
  .properties-grid {
    grid-template-columns: 1fr;
  }
}

/* Property Card */
.property-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.property-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.property-image {
  position: relative;
  height: 200px;
  background-color: var(--color-border-light);
  overflow: hidden;
}

.property-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.property-card:hover .property-image img {
  transform: scale(1.05);
}

.property-status {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: var(--color-success);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.property-content {
  padding: var(--spacing-lg);
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-md);
}

.property-address {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
  flex: 1;
}

.property-price {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--color-primary);
  white-space: nowrap;
}

.property-details {
  margin-bottom: var(--spacing-md);
}

.property-meta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.meta-item .icon {
  width: 1rem;
  height: 1rem;
}

.property-mls {
  font-family: var(--font-family-mono);
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

.property-description {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.property-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.property-timestamp {
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-muted);
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto var(--spacing-lg);
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-secondary);
}

.empty-state p {
  font-size: 0.875rem;
}

/* Activity Feed */
.activity-feed {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-family: var(--font-family-mono);
  font-size: 0.75rem;
  color: var(--color-text-muted);
  white-space: nowrap;
  min-width: 80px;
}

.activity-message {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  flex: 1;
}

/* Configuration Panel */
.config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.config-overlay.active {
  opacity: 1;
  visibility: visible;
}

.config-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 400px;
  height: 100%;
  background-color: var(--color-surface);
  box-shadow: var(--shadow-lg);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  overflow-y: auto;
}

.config-overlay.active .config-panel {
  transform: translateX(0);
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}

.config-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.config-close {
  padding: var(--spacing-sm);
}

.config-form {
  padding: var(--spacing-lg);
}

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-group input[type="text"],
.form-group input[type="number"] {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(13, 115, 119, 0.1);
}

.form-help {
  display: block;
  font-size: 0.75rem;
  color: var(--color-text-muted);
  margin-top: var(--spacing-xs);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-weight: 400;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-sm);
  background-color: var(--color-surface);
  position: relative;
  transition: all var(--transition-fast);
}

.checkbox-label input:checked + .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-label input:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  position: relative;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:disabled:hover {
  transform: none;
}

/* Loading spinner for buttons */
.btn.loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn .icon {
  width: 1rem;
  height: 1rem;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0A5D61;
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #E6954A;
}

.btn-outline {
  border-color: var(--color-border);
  background-color: var(--color-surface);
  color: var(--color-text-primary);
}

.btn-outline:hover:not(:disabled) {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-border-light);
  color: var(--color-text-primary);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.toast {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  border-left: 4px solid var(--color-success);
}

.toast.error {
  border-left: 4px solid var(--color-error);
}

.toast.warning {
  border-left: 4px solid var(--color-alert);
}

.toast.info {
  border-left: 4px solid var(--color-primary);
}

/* Connection Status Indicator */
.connection-status {
  position: fixed;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.75rem;
  color: var(--color-text-muted);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  z-index: 1000;
  opacity: 0;
  transform: translateY(100%);
  transition: all var(--transition-normal);
}

.connection-status.show {
  opacity: 1;
  transform: translateY(0);
}

.connection-status.connected {
  border-color: var(--color-success);
  color: var(--color-success);
}

.connection-status.disconnected {
  border-color: var(--color-error);
  color: var(--color-error);
}

.connection-status .status-dot {
  width: 0.5rem;
  height: 0.5rem;
}

/* Form Validation Styles */
.form-group.error input {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
  color: var(--color-error);
  font-size: 0.75rem;
  margin-top: var(--spacing-xs);
  display: block;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: inherit;
}

[data-theme="dark"] .loading-overlay {
  background-color: rgba(26, 26, 26, 0.8);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: space-between;
  }
  
  .status-details {
    grid-template-columns: 1fr;
  }
  
  .status-actions {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
  
  .property-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .property-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .config-panel {
    max-width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .main {
    padding: var(--spacing-lg) 0;
  }
  
  .status-card {
    padding: var(--spacing-lg);
  }
  
  .property-content {
    padding: var(--spacing-md);
  }
  
  .toast-container {
    left: var(--spacing-sm);
    right: var(--spacing-sm);
  }
  
  .toast {
    max-width: none;
  }
}

/* Print Styles */
@media print {
  .header-controls,
  .status-actions,
  .property-actions,
  .config-overlay,
  .toast-container {
    display: none !important;
  }
  
  .property-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}