"""
Address matching system for the Real Estate Monitor application.
Provides fuzzy string matching capabilities to handle address variations.
"""
import re
from difflib import SequenceMatcher
from typing import List, Tuple
from .models import PropertyListing


class AddressMatcher:
    """
    Handles address matching with fuzzy string matching and normalization.
    Supports common address variations and typos.
    """
    
    # Common street type abbreviations mapping
    STREET_TYPE_MAPPINGS = {
        'ST': 'STREET',
        'STREET': 'STREET',
        'AVE': 'AVENUE',
        'AVENUE': 'AVENUE',
        'RD': 'ROAD',
        'ROAD': 'ROAD',
        'DR': 'DRIVE',
        'DRIVE': 'DRIVE',
        'CT': 'COURT',
        'COURT': 'COURT',
        'PL': 'PLACE',
        'PLACE': 'PLACE',
        'LN': 'LANE',
        'LANE': 'LANE',
        'WAY': 'WAY',
        'BLVD': 'BOULEVARD',
        'BOULEVARD': 'BOULEVARD',
        'CIR': 'CIRCLE',
        'CIRCLE': 'CIRCLE',
        'CRES': 'CRESCENT',
        'CRESCENT': 'CRESCENT',
        'TERR': 'TERRACE',
        'TERRACE': 'TERRACE',
        'PKWY': 'PARKWAY',
        'PARKWAY': 'PARKWAY'
    }
    
    # Direction abbreviations
    DIRECTION_MAPPINGS = {
        'N': 'NORTH',
        'NORTH': 'NORTH',
        'S': 'SOUTH',
        'SOUTH': 'SOUTH',
        'E': 'EAST',
        'EAST': 'EAST',
        'W': 'WEST',
        'WEST': 'WEST',
        'NE': 'NORTHEAST',
        'NORTHEAST': 'NORTHEAST',
        'NW': 'NORTHWEST',
        'NORTHWEST': 'NORTHWEST',
        'SE': 'SOUTHEAST',
        'SOUTHEAST': 'SOUTHEAST',
        'SW': 'SOUTHWEST',
        'SOUTHWEST': 'SOUTHWEST'
    }
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        Initialize the AddressMatcher.
        
        Args:
            similarity_threshold: Minimum similarity score (0.0-1.0) to consider a match
        """
        if not 0.0 <= similarity_threshold <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
        self.similarity_threshold = similarity_threshold
    
    def normalize_address(self, address: str) -> str:
        """
        Normalize an address string for consistent comparison.
        
        Args:
            address: Raw address string
            
        Returns:
            Normalized address string
        """
        if not address or not isinstance(address, str):
            return ""
        
        # Convert to uppercase and strip whitespace
        normalized = address.upper().strip()
        
        # Remove extra whitespace (multiple spaces, tabs, newlines)
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Remove common punctuation that doesn't affect matching
        normalized = re.sub(r'[,.]', '', normalized)
        
        # Normalize street types
        words = normalized.split()
        normalized_words = []
        
        for word in words:
            # Check if word is a street type abbreviation
            if word in self.STREET_TYPE_MAPPINGS:
                normalized_words.append(self.STREET_TYPE_MAPPINGS[word])
            # Check if word is a direction abbreviation
            elif word in self.DIRECTION_MAPPINGS:
                normalized_words.append(self.DIRECTION_MAPPINGS[word])
            else:
                normalized_words.append(word)
        
        return ' '.join(normalized_words)
    
    def _extract_house_number(self, normalized_address: str) -> str:
        """
        Extract the house number from a normalized address.
        
        Args:
            normalized_address: Normalized address string
            
        Returns:
            House number as string, or empty string if not found
        """
        if not normalized_address:
            return ""
        
        # Look for number at the beginning of the address
        match = re.match(r'^(\d+)', normalized_address.strip())
        return match.group(1) if match else ""
    
    def calculate_similarity(self, addr1: str, addr2: str) -> float:
        """
        Calculate similarity score between two addresses.
        
        Args:
            addr1: First address string
            addr2: Second address string
            
        Returns:
            Similarity score between 0.0 and 1.0
        """
        if not addr1 or not addr2:
            return 0.0
        
        # Normalize both addresses
        norm_addr1 = self.normalize_address(addr1)
        norm_addr2 = self.normalize_address(addr2)
        
        if not norm_addr1 or not norm_addr2:
            return 0.0
        
        # Check for exact match first
        if norm_addr1 == norm_addr2:
            return 1.0
        
        # Extract house numbers for comparison
        house_num1 = self._extract_house_number(norm_addr1)
        house_num2 = self._extract_house_number(norm_addr2)
        
        # If house numbers are different, significantly reduce similarity
        if house_num1 and house_num2 and house_num1 != house_num2:
            # Use difflib for fuzzy matching but cap at 0.7 for different house numbers
            matcher = SequenceMatcher(None, norm_addr1, norm_addr2)
            base_similarity = matcher.ratio()
            return min(base_similarity * 0.7, 0.7)  # Cap at 0.7 for different house numbers
        
        # Check for substring match (one address contains the other)
        if norm_addr1 in norm_addr2 or norm_addr2 in norm_addr1:
            return 0.95
        
        # Use difflib for fuzzy matching
        matcher = SequenceMatcher(None, norm_addr1, norm_addr2)
        return matcher.ratio()
    
    def is_match(self, target_address: str, listing_address: str) -> bool:
        """
        Determine if two addresses match based on similarity threshold.
        
        Args:
            target_address: The address we're looking for
            listing_address: The address from a property listing
            
        Returns:
            True if addresses match, False otherwise
        """
        similarity = self.calculate_similarity(target_address, listing_address)
        return similarity >= self.similarity_threshold
    
    def find_matches(self, target_address: str, listings: List[PropertyListing]) -> List[Tuple[PropertyListing, float]]:
        """
        Find all property listings that match the target address.
        
        Args:
            target_address: The address to search for
            listings: List of property listings to search through
            
        Returns:
            List of tuples containing (PropertyListing, similarity_score) for matches,
            sorted by similarity score in descending order
        """
        if not target_address or not listings:
            return []
        
        matches = []
        
        for listing in listings:
            similarity = self.calculate_similarity(target_address, listing.address)
            if similarity >= self.similarity_threshold:
                matches.append((listing, similarity))
        
        # Sort by similarity score in descending order
        matches.sort(key=lambda x: x[1], reverse=True)
        
        return matches
    
    def find_best_match(self, target_address: str, listings: List[PropertyListing]) -> Tuple[PropertyListing, float] or None:
        """
        Find the best matching property listing for the target address.
        
        Args:
            target_address: The address to search for
            listings: List of property listings to search through
            
        Returns:
            Tuple of (PropertyListing, similarity_score) for the best match,
            or None if no matches found
        """
        matches = self.find_matches(target_address, listings)
        return matches[0] if matches else None
    
    def get_similarity_explanation(self, target_address: str, listing_address: str) -> dict:
        """
        Get detailed explanation of similarity calculation for debugging.
        
        Args:
            target_address: The target address
            listing_address: The listing address
            
        Returns:
            Dictionary with similarity details
        """
        norm_target = self.normalize_address(target_address)
        norm_listing = self.normalize_address(listing_address)
        similarity = self.calculate_similarity(target_address, listing_address)
        
        return {
            'original_target': target_address,
            'original_listing': listing_address,
            'normalized_target': norm_target,
            'normalized_listing': norm_listing,
            'similarity_score': similarity,
            'is_match': similarity >= self.similarity_threshold,
            'threshold': self.similarity_threshold
        }