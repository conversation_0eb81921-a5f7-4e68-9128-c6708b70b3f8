# Implementation Plan

- [x] 1. Create base scraper interface and refactor existing scraper






  - Create abstract base class `BaseScraper` in `backend/base_scraper.py`
  - Define interface methods: `fetch_listings()`, `get_source_name()`
  - Refactor `<PERSON>NorthScraper` to inherit from `BaseScraper`
  - Add `get_source_name()` method returning "meadownorth.ca"
  - _Requirements: 3.2, 3.3_

- [x] 2. Enhance PropertyListing model with source field





  - Add `source: str = ""` field to PropertyListing dataclass in `shared/models.py`
  - Update `__post_init__` validation if needed
  - Ensure backward compatibility with existing code
  - _Requirements: 2.1, 2.2_

- [x] 3. Implement RE/MAX scraper class





  - Create `backend/remax_scraper.py` with `RemaxScraper` class
  - Implement `__init__` method with base URL configuration
  - Implement `get_source_name()` method returning "remax.ca"
  - Add session configuration and rate limiting setup
  - _Requirements: 1.1, 3.1, 3.2_

- [x] 4. Implement RE/MAX pagination handling





  - Add `_handle_pagination()` method to fetch all pages
  - Implement URL construction with pageNumber parameter
  - Add logic to detect empty pages and stop pagination
  - Include rate limiting between page requests
  - _Requirements: 1.2, 4.2, 4.3_

- [ ] 5. Implement RE/MAX HTML parsing and data extraction




  - Add `fetch_listings()` method using pagination
  - Implement `parse_listing_html()` for RE/MAX HTML structure
  - Add `extract_property_data()` for individual property elements
  - Implement all extraction methods (`_extract_price`, `_extract_address`, etc.)
  - _Requirements: 1.3, 2.3, 5.1, 5.2, 5.3, 5.4_

- [ ] 6. Add error handling and retry logic to RE/MAX scraper
  - Implement exponential backoff retry logic
  - Add network error handling (timeouts, connection errors)
  - Include parsing error handling for individual listings
  - Add comprehensive logging with RE/MAX source prefix
  - _Requirements: 1.4, 6.1, 6.3, 6.4_

- [ ] 7. Enhance monitoring service for multiple scrapers
  - Modify `MonitoringService.__init__` to accept list of scrapers
  - Update `_fetch_and_check_listings()` to aggregate from all scrapers
  - Add source tagging for listings from each scraper
  - Implement partial failure handling (continue if one scraper fails)
  - _Requirements: 3.3, 3.4, 6.2_

- [ ] 8. Update Flask app initialization for multiple scrapers
  - Modify `backend/app.py` to initialize both scrapers
  - Create scraper list and pass to monitoring service
  - Ensure existing API endpoints work with aggregated results
  - Test that web interface displays listings from both sources
  - _Requirements: 7.1, 7.3_

- [ ] 9. Add comprehensive error handling and logging
  - Update monitoring service logging to identify scraper sources
  - Ensure notifications specify which source found/lost properties
  - Add health check endpoint updates for multiple scrapers
  - Test error scenarios (one scraper down, parsing failures)
  - _Requirements: 6.2, 6.3, 7.2, 7.4_

- [ ] 10. Create unit tests for RE/MAX scraper
  - Write tests for pagination logic with mock responses
  - Test HTML parsing with sample RE/MAX HTML
  - Test error handling and retry mechanisms
  - Test data extraction methods with various property formats
  - _Requirements: 1.1, 1.2, 1.3, 5.1, 5.2, 5.3, 5.4_

- [ ] 11. Create integration tests for multi-source monitoring
  - Test monitoring service with both scrapers enabled
  - Test partial failure scenarios (one scraper fails)
  - Test address matching across different sources
  - Test that notifications work for properties from both sources
  - _Requirements: 3.3, 3.4, 6.2, 7.2, 7.4_