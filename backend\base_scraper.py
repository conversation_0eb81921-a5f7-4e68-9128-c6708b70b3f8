"""
Abstract base class for property listing scrapers.
"""
from abc import ABC, abstractmethod
from typing import List
from shared.models import PropertyListing


class BaseScraper(ABC):
    """Abstract base class for property listing scrapers"""
    
    @abstractmethod
    def fetch_listings(self) -> List[PropertyListing]:
        """
        Fetch and parse all property listings from the source
        
        Returns:
            List of PropertyListing objects
            
        Raises:
            requests.RequestException: For network-related errors
            ValueError: For parsing errors
        """
        pass
    
    @abstractmethod
    def get_source_name(self) -> str:
        """
        Get the name of the data source
        
        Returns:
            String identifier for the data source
        """
        pass