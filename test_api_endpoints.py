#!/usr/bin/env python3
"""
Simple API endpoint test using direct HTTP requests
"""
import subprocess
import json
import time
import threading
from backend.app import RealEstateMonitorAPI

def start_server_for_curl_test():
    """Start server for curl testing"""
    try:
        api = RealEstateMonitorAPI()
        api.app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False, threaded=True)
    except Exception as e:
        print(f"Server error: {e}")

def test_with_curl():
    """Test API endpoints using curl commands"""
    print("Starting server for curl tests...")
    server_thread = threading.Thread(target=start_server_for_curl_test, daemon=True)
    server_thread.start()
    time.sleep(2)
    
    base_url = "http://localhost:5001"
    
    print("\n=== CURL TESTS ===")
    
    # Test GET /api/config
    print("\n1. Testing GET /api/config:")
    try:
        result = subprocess.run([
            'curl', '-s', '-X', 'GET', 
            f'{base_url}/api/config',
            '-H', 'Content-Type: application/json'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✓ GET request successful")
            print(f"Response: {result.stdout}")
        else:
            print(f"✗ GET request failed: {result.stderr}")
    except Exception as e:
        print(f"✗ Error with GET request: {e}")
    
    # Test POST /api/config
    print("\n2. Testing POST /api/config:")
    test_data = {
        "target_address": "456 Curl Test Avenue",
        "check_interval_minutes": 45,
        "notification_enabled": False
    }
    
    try:
        result = subprocess.run([
            'curl', '-s', '-X', 'POST',
            f'{base_url}/api/config',
            '-H', 'Content-Type: application/json',
            '-d', json.dumps(test_data)
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✓ POST request successful")
            print(f"Response: {result.stdout}")
        else:
            print(f"✗ POST request failed: {result.stderr}")
    except Exception as e:
        print(f"✗ Error with POST request: {e}")

if __name__ == "__main__":
    test_with_curl()