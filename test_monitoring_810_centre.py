#!/usr/bin/env python3
"""
Test the monitoring service with the specific address "810 Centre STREET"
"""
import sys
import logging
from datetime import datetime
from backend.scraper import Meadow<PERSON><PERSON>hScraper
from backend.monitoring_service import MonitoringService
from shared.address_matcher import AddressMatcher
from shared.config_manager import ConfigManager
from shared.models import Config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_monitoring():
    """Test the monitoring service for 810 Centre STREET"""
    target_address = "810 Centre STREET"
    
    print(f"🔍 Testing monitoring service for: {target_address}")
    print("=" * 60)
    
    try:
        # Create a test config
        config = Config(
            target_address=target_address,
            check_interval_minutes=1,  # Short interval for testing
            notification_enabled=False
        )
        
        # Initialize components
        config_manager = ConfigManager()
        config_manager.save_config(config)
        
        scraper = MeadowNorthScraper()
        address_matcher = AddressMatcher(similarity_threshold=0.8)
        
        # Create monitoring service
        monitoring_service = MonitoringService(
            config_manager=config_manager,
            scraper=scraper,
            address_matcher=address_matcher
        )
        
        print("✅ Monitoring service initialized")
        
        # Perform a single check
        print("🔍 Performing property check...")
        found_listings = monitoring_service.perform_immediate_check()
        
        # Also check the saved config
        saved_config = config_manager.load_config()
        saved_listings = saved_config.found_listings
        
        print(f"📋 Found {len(found_listings)} matching listings from immediate check:")
        for listing in found_listings:
            print(f"  - {listing.address} - {listing.price} (MLS: {listing.mls_number})")
            print(f"    Description: {listing.description[:100]}...")
        
        print(f"📋 Found {len(saved_listings)} saved listings in config:")
        for listing in saved_listings:
            if hasattr(listing, 'address'):
                print(f"  - {listing.address} - {listing.price} (MLS: {listing.mls_number})")
            else:
                print(f"  - {listing}")
        
        if found_listings or saved_listings:
            print("✅ SUCCESS: The monitoring service found the target address!")
        else:
            print("❌ FAILURE: The monitoring service did not find the target address")
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        logger.exception("Test failed")

if __name__ == "__main__":
    test_monitoring()