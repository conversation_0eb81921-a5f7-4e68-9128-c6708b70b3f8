"""
Unit tests for ConfigManager class.
"""
import json
import os
import tempfile
import unittest
from datetime import datetime
from pathlib import Path
from unittest.mock import patch, mock_open

from shared.config_manager import ConfigManager
from shared.models import Config


class TestConfigManager(unittest.TestCase):
    """Test cases for ConfigManager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(
            config_file="test_config.json",
            data_dir=self.test_dir
        )
    
    def tearDown(self):
        """Clean up test fixtures"""
        # Remove test files
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_init_creates_data_directory(self):
        """Test that ConfigManager creates data directory on initialization"""
        new_test_dir = os.path.join(self.test_dir, "new_data_dir")
        config_manager = ConfigManager(data_dir=new_test_dir)
        
        self.assertTrue(os.path.exists(new_test_dir))
        self.assertTrue(os.path.isdir(new_test_dir))
    
    def test_load_config_creates_default_when_file_missing(self):
        """Test that load_config creates default config when file doesn't exist"""
        config = self.config_manager.load_config()
        
        self.assertIsInstance(config, Config)
        self.assertEqual(config.target_address, "1 PLACEHOLDER STREET")
        self.assertEqual(config.check_interval_minutes, 15)
        self.assertTrue(config.notification_enabled)
        self.assertIsNone(config.last_check)
    
    def test_load_config_handles_corrupted_file(self):
        """Test that load_config handles corrupted JSON files gracefully"""
        # Create corrupted config file
        config_file_path = self.config_manager.get_config_file_path()
        with open(config_file_path, 'w') as f:
            f.write("{ invalid json content")
        
        # Should return default config instead of crashing
        config = self.config_manager.load_config()
        
        self.assertIsInstance(config, Config)
        self.assertEqual(config.target_address, "1 PLACEHOLDER STREET")
    
    def test_save_config_includes_schema_version(self):
        """Test that save_config includes schema version in saved data"""
        config = Config(
            target_address="123 Test Street",
            check_interval_minutes=10,
            notification_enabled=False
        )
        
        self.config_manager.save_config(config)
        
        # Read the raw JSON to check schema version
        config_file_path = self.config_manager.get_config_file_path()
        with open(config_file_path, 'r') as f:
            raw_data = json.load(f)
        
        self.assertIn("schema_version", raw_data)
        self.assertEqual(raw_data["schema_version"], "1.0")
    
    def test_load_config_with_migration(self):
        """Test that load_config applies data migration for old format"""
        # Create old format config file (without schema version)
        old_config_data = {
            "target_address": "456 Old Street",
            "check_interval_minutes": 20
            # Missing some fields that should be added by migration
        }
        
        config_file_path = self.config_manager.get_config_file_path()
        with open(config_file_path, 'w') as f:
            json.dump(old_config_data, f)
        
        # Load config (should trigger migration)
        config = self.config_manager.load_config()
        
        self.assertEqual(config.target_address, "456 Old Street")
        self.assertEqual(config.check_interval_minutes, 20)
        self.assertTrue(config.notification_enabled)  # Should be added by migration
        self.assertIsNone(config.last_check)
        self.assertEqual(config.found_listings, [])
        self.assertEqual(config.found_listings, [])
    
    def test_save_and_load_config(self):
        """Test saving and loading configuration"""
        # Create a test config
        test_config = Config(
            target_address="123 Test Street",
            check_interval_minutes=30,
            notification_enabled=False,
            last_check=datetime(2024, 1, 1, 12, 0, 0),
            found_listings=["MLS123", "MLS456"]
        )
        
        # Save the config
        self.config_manager.save_config(test_config)
        
        # Load it back
        loaded_config = self.config_manager.load_config()
        
        self.assertEqual(loaded_config.target_address, "123 Test Street")
        self.assertEqual(loaded_config.check_interval_minutes, 30)
        self.assertFalse(loaded_config.notification_enabled)
        self.assertEqual(loaded_config.last_check, datetime(2024, 1, 1, 12, 0, 0))
        self.assertEqual(loaded_config.found_listings, ["MLS123", "MLS456"])
    
    def test_save_config_invalid_type(self):
        """Test that save_config raises error for invalid config type"""
        with self.assertRaises(ValueError) as context:
            self.config_manager.save_config("not a config")
        
        self.assertIn("config must be a Config instance", str(context.exception))
    
    def test_load_config_invalid_json(self):
        """Test that load_config handles invalid JSON gracefully"""
        # Write invalid JSON to config file
        config_file = self.config_manager.config_file
        with open(config_file, 'w') as f:
            f.write("{ invalid json }")
        
        # Should return default config instead of raising error
        config = self.config_manager.load_config()
        
        self.assertIsInstance(config, Config)
        self.assertEqual(config.target_address, "1 PLACEHOLDER STREET")
    
    def test_load_config_missing_required_field(self):
        """Test that load_config handles missing required fields"""
        # Write config without target_address
        config_file = self.config_manager.config_file
        with open(config_file, 'w') as f:
            json.dump({"check_interval_minutes": 15}, f)
        
        # Migration should add missing fields
        config = self.config_manager.load_config()
        
        self.assertIsInstance(config, Config)
        self.assertEqual(config.target_address, "1 PLACEHOLDER STREET")  # Added by migration
        self.assertEqual(config.check_interval_minutes, 15)
    
    def test_get_target_address(self):
        """Test getting target address"""
        # Save a config with target address
        test_config = Config(target_address="456 Main Street")
        self.config_manager.save_config(test_config)
        
        address = self.config_manager.get_target_address()
        self.assertEqual(address, "456 Main Street")
    
    def test_set_target_address(self):
        """Test setting target address"""
        self.config_manager.set_target_address("789 Oak Avenue")
        
        # Verify it was saved
        config = self.config_manager.load_config()
        self.assertEqual(config.target_address, "789 Oak Avenue")
    
    def test_set_target_address_empty(self):
        """Test that setting empty target address raises error"""
        with self.assertRaises(ValueError) as context:
            self.config_manager.set_target_address("")
        
        self.assertIn("Address cannot be empty", str(context.exception))
        
        with self.assertRaises(ValueError):
            self.config_manager.set_target_address("   ")
    
    def test_set_target_address_strips_whitespace(self):
        """Test that setting target address strips whitespace"""
        self.config_manager.set_target_address("  123 Test St  ")
        
        config = self.config_manager.load_config()
        self.assertEqual(config.target_address, "123 Test St")
    
    def test_get_check_interval(self):
        """Test getting check interval"""
        # Save a config with custom interval
        test_config = Config(target_address="123 Test St", check_interval_minutes=45)
        self.config_manager.save_config(test_config)
        
        interval = self.config_manager.get_check_interval()
        self.assertEqual(interval, 45)
    
    def test_set_check_interval(self):
        """Test setting check interval"""
        # First set a valid target address
        self.config_manager.set_target_address("123 Test St")
        
        self.config_manager.set_check_interval(60)
        
        # Verify it was saved
        config = self.config_manager.load_config()
        self.assertEqual(config.check_interval_minutes, 60)
    
    def test_set_check_interval_invalid(self):
        """Test that setting invalid check interval raises error"""
        with self.assertRaises(ValueError) as context:
            self.config_manager.set_check_interval(0)
        
        self.assertIn("Check interval must be positive", str(context.exception))
        
        with self.assertRaises(ValueError):
            self.config_manager.set_check_interval(-5)
    
    def test_update_last_check(self):
        """Test updating last check timestamp"""
        # First set a valid target address
        self.config_manager.set_target_address("123 Test St")
        
        test_time = datetime(2024, 2, 1, 15, 30, 0)
        self.config_manager.update_last_check(test_time)
        
        config = self.config_manager.load_config()
        self.assertEqual(config.last_check, test_time)
    
    def test_update_last_check_default_time(self):
        """Test updating last check with default (current) time"""
        # First set a valid target address
        self.config_manager.set_target_address("123 Test St")
        
        before_time = datetime.now()
        self.config_manager.update_last_check()
        after_time = datetime.now()
        
        config = self.config_manager.load_config()
        self.assertIsNotNone(config.last_check)
        self.assertGreaterEqual(config.last_check, before_time)
        self.assertLessEqual(config.last_check, after_time)
    
    def test_add_found_listing(self):
        """Test adding found listing"""
        # First set a valid target address
        self.config_manager.set_target_address("123 Test St")
        
        self.config_manager.add_found_listing("MLS789")
        
        config = self.config_manager.load_config()
        self.assertIn("MLS789", config.found_listings)
    
    def test_add_found_listing_duplicate(self):
        """Test that adding duplicate listing doesn't create duplicates"""
        # First set a valid target address
        self.config_manager.set_target_address("123 Test St")
        
        self.config_manager.add_found_listing("MLS789")
        self.config_manager.add_found_listing("MLS789")  # Add same listing again
        
        config = self.config_manager.load_config()
        self.assertEqual(config.found_listings.count("MLS789"), 1)
    
    def test_add_found_listing_empty(self):
        """Test that adding empty MLS number raises error"""
        with self.assertRaises(ValueError) as context:
            self.config_manager.add_found_listing("")
        
        self.assertIn("MLS number cannot be empty", str(context.exception))
    
    def test_remove_found_listing(self):
        """Test removing found listing"""
        # First set a valid target address and add a listing
        self.config_manager.set_target_address("123 Test St")
        self.config_manager.add_found_listing("MLS789")
        
        # Verify it was added
        config = self.config_manager.load_config()
        self.assertIn("MLS789", config.found_listings)
        
        # Remove it
        self.config_manager.remove_found_listing("MLS789")
        
        # Verify it was removed
        config = self.config_manager.load_config()
        self.assertNotIn("MLS789", config.found_listings)
    
    def test_remove_found_listing_not_exists(self):
        """Test removing listing that doesn't exist (should not raise error)"""
        # First set a valid target address
        self.config_manager.set_target_address("123 Test St")
        
        # This should not raise an error
        self.config_manager.remove_found_listing("MLS999")
    
    def test_remove_found_listing_empty(self):
        """Test that removing empty MLS number raises error"""
        with self.assertRaises(ValueError) as context:
            self.config_manager.remove_found_listing("")
        
        self.assertIn("MLS number cannot be empty", str(context.exception))
    
    def test_is_first_run(self):
        """Test first run detection"""
        # Should be first run initially
        self.assertTrue(self.config_manager.is_first_run())
        
        # After saving config, should not be first run
        test_config = Config(target_address="123 Test St")
        self.config_manager.save_config(test_config)
        self.assertFalse(self.config_manager.is_first_run())
    
    def test_needs_address_setup(self):
        """Test address setup detection"""
        # Should need setup initially (using placeholder)
        self.assertTrue(self.config_manager.needs_address_setup())
        
        # After setting real address, should not need setup
        self.config_manager.set_target_address("123 Test St")
        self.assertFalse(self.config_manager.needs_address_setup())
    
    def test_validate_address_format_valid(self):
        """Test address format validation with valid addresses"""
        valid_addresses = [
            "123 Main Street",
            "456 Oak Avenue",
            "789 First St",
            "1 Broadway",
            "1234 Some Long Street Name"
        ]
        
        for address in valid_addresses:
            with self.subTest(address=address):
                self.assertTrue(self.config_manager.validate_address_format(address))
    
    def test_validate_address_format_invalid(self):
        """Test address format validation with invalid addresses"""
        invalid_addresses = [
            "",
            "   ",
            "Main Street",  # No number
            "123",  # No street name
            "   123   ",  # Only number with spaces
        ]
        
        for address in invalid_addresses:
            with self.subTest(address=address):
                self.assertFalse(self.config_manager.validate_address_format(address))
    
    def test_get_config_file_path(self):
        """Test getting config file path"""
        path = self.config_manager.get_config_file_path()
        self.assertIsInstance(path, Path)
        self.assertEqual(path.name, "test_config.json")
    
    def test_get_data_dir_path(self):
        """Test getting data directory path"""
        path = self.config_manager.get_data_dir_path()
        self.assertIsInstance(path, Path)
        self.assertEqual(str(path), self.test_dir)
    
    @patch('builtins.open', side_effect=PermissionError("Permission denied"))
    def test_load_config_permission_error(self, mock_file):
        """Test handling permission errors when loading config"""
        # Create the config file first so it exists
        config_file = self.config_manager.config_file
        config_file.touch()
        
        with self.assertRaises(ValueError) as context:
            self.config_manager.load_config()
        
        self.assertIn("Permission denied reading", str(context.exception))
    
    @patch('builtins.open', side_effect=PermissionError("Permission denied"))
    def test_save_config_permission_error(self, mock_file):
        """Test handling permission errors when saving config"""
        test_config = Config(target_address="123 Test St")
        
        with self.assertRaises(IOError) as context:
            self.config_manager.save_config(test_config)
        
        self.assertIn("Failed to write", str(context.exception))


if __name__ == '__main__':
    unittest.main()