"""
Core data models for the Real Estate Monitor application.
"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, List
import re


@dataclass
class PropertyListing:
    """Represents a property listing from real estate websites"""
    address: str
    price: str
    mls_number: str
    description: str
    source: str = ""
    square_footage: Optional[str] = None
    bedrooms: Optional[int] = None
    bathrooms: Optional[int] = None
    lot_size: Optional[str] = None
    year_built: Optional[str] = None
    image_url: str = ""
    listing_url: str = ""
    first_seen: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Validate property listing data after initialization"""
        if not self.address or not self.address.strip():
            raise ValueError("Address cannot be empty")
        
        if not self.price or not self.price.strip():
            raise ValueError("Price cannot be empty")
        
        if not self.mls_number or not self.mls_number.strip():
            raise ValueError("MLS number cannot be empty")
        
        # Normalize address
        self.address = self.address.strip()
        
        # Normalize source field
        if self.source:
            self.source = self.source.strip()
        
        # Validate bedrooms and bathrooms are non-negative if provided
        if self.bedrooms is not None and self.bedrooms < 0:
            raise ValueError("Bedrooms must be non-negative")
        
        if self.bathrooms is not None and self.bathrooms < 0:
            raise ValueError("Bathrooms must be non-negative")


@dataclass
class Config:
    """Application configuration settings"""
    target_address: str
    check_interval_minutes: int = 15
    notification_enabled: bool = True
    last_check: Optional[datetime] = None
    found_listings: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Validate configuration data after initialization"""
        if not self.target_address or not self.target_address.strip():
            raise ValueError("Target address cannot be empty")
        
        if self.check_interval_minutes <= 0:
            raise ValueError("Check interval must be positive")
        
        if self.check_interval_minutes < 1:
            raise ValueError("Check interval must be at least 1 minute")
        
        # Normalize target address
        self.target_address = self.target_address.strip()
        
        # Basic address format validation
        if not self._is_valid_address_format(self.target_address):
            raise ValueError("Target address format appears invalid")
    
    def _is_valid_address_format(self, address: str) -> bool:
        """Basic validation for address format"""
        # Check if address contains at least a number and some text
        # This is a basic check - more sophisticated validation could be added
        pattern = r'^\d+.*[a-zA-Z].*'
        return bool(re.match(pattern, address.strip()))
    
    def to_dict(self) -> dict:
        """Convert config to dictionary for JSON serialization"""
        return {
            'target_address': self.target_address,
            'check_interval_minutes': self.check_interval_minutes,
            'notification_enabled': self.notification_enabled,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'found_listings': self.found_listings
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Config':
        """Create config from dictionary (for JSON deserialization)"""
        last_check = None
        if data.get('last_check'):
            last_check = datetime.fromisoformat(data['last_check'])
        
        return cls(
            target_address=data['target_address'],
            check_interval_minutes=data.get('check_interval_minutes', 15),
            notification_enabled=data.get('notification_enabled', True),
            last_check=last_check,
            found_listings=data.get('found_listings', [])
        )