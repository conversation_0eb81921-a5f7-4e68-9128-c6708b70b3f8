"""
Background monitoring service for the Real Estate Monitor application.
Handles continuous monitoring of property listings in a separate thread.
"""
import threading
import time
import logging
from datetime import datetime
from typing import List, Optional, Callable, Set
from dataclasses import dataclass

from shared.config_manager import ConfigManager
from shared.models import Config, PropertyListing
from shared.address_matcher import AddressMatcher
from shared.data_persistence import DataPersistenceManager
from backend.scraper import Meadow<PERSON>orthScraper


@dataclass
class MonitoringEvent:
    """Represents a monitoring event (property found/removed)"""
    event_type: str  # 'property_found', 'property_removed', 'error', 'status_change'
    timestamp: datetime
    message: str
    property_listing: Optional[PropertyListing] = None
    error: Optional[Exception] = None


class MonitoringService:
    """
    Background service that monitors property listings in a separate thread.
    Handles periodic checking, property comparison, and event notifications.
    """
    
    def __init__(self, config_manager: ConfigManager, scraper: MeadowNorthScraper, 
                 address_matcher: AddressMatcher, data_dir: str = "data"):
        """
        Initialize the monitoring service.
        
        Args:
            config_manager: Configuration manager instance
            scraper: Web scraper instance
            address_matcher: Address matching instance
            data_dir: Directory for data persistence
        """
        self.config_manager = config_manager
        self.scraper = scraper
        self.address_matcher = address_matcher
        
        # Data persistence
        self.persistence_manager = DataPersistenceManager(data_dir)
        
        # Threading and state management
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._is_running = False
        self._current_status = "stopped"
        
        # Monitoring state
        self.current_listings: List[PropertyListing] = []
        self.last_check_time: Optional[datetime] = None
        self.found_properties: Set[str] = set()  # MLS numbers of found properties
        
        # Event callbacks
        self._event_callbacks: List[Callable[[MonitoringEvent], None]] = []
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Load initial state from persistence
        self._load_initial_state()
    
    def _load_initial_state(self):
        """Load initial monitoring state from persistence and configuration"""
        try:
            # Load from configuration (for found listings)
            config = self.config_manager.load_config()
            self.found_properties = set(config.found_listings)
            self.last_check_time = config.last_check
            
            # Load monitoring state from persistence
            monitoring_state = self.persistence_manager.load_monitoring_state()
            
            # Restore monitoring state if available
            if monitoring_state:
                # Don't restore running state - always start stopped
                self._current_status = monitoring_state.get("current_status", "stopped")
                if self._current_status == "active":
                    self._current_status = "stopped"
                
                # Restore last check time if more recent than config
                state_last_check = monitoring_state.get("last_check_time")
                if state_last_check:
                    try:
                        state_check_time = datetime.fromisoformat(state_last_check)
                        if not self.last_check_time or state_check_time > self.last_check_time:
                            self.last_check_time = state_check_time
                    except ValueError:
                        pass
                
                # Load historical listings
                self.current_listings = self.persistence_manager.load_listings_history()
                
        except Exception as e:
            self.logger.warning(f"Could not load initial state: {e}")
            self.found_properties = set()
            self.last_check_time = None
            self.current_listings = []
    
    def add_event_callback(self, callback: Callable[[MonitoringEvent], None]):
        """
        Add a callback function to be called when monitoring events occur.
        
        Args:
            callback: Function that takes a MonitoringEvent as parameter
        """
        if callback not in self._event_callbacks:
            self._event_callbacks.append(callback)
    
    def remove_event_callback(self, callback: Callable[[MonitoringEvent], None]):
        """
        Remove an event callback.
        
        Args:
            callback: Callback function to remove
        """
        if callback in self._event_callbacks:
            self._event_callbacks.remove(callback)
    
    def _emit_event(self, event: MonitoringEvent):
        """
        Emit a monitoring event to all registered callbacks.
        
        Args:
            event: MonitoringEvent to emit
        """
        for callback in self._event_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"Error in event callback: {e}")
    
    def start_monitoring(self) -> bool:
        """
        Start the monitoring service in a background thread.
        
        Returns:
            bool: True if monitoring started successfully, False otherwise
        """
        if self._is_running:
            self.logger.warning("Monitoring service is already running")
            return False
        
        try:
            # Validate configuration before starting
            config = self.config_manager.load_config()
            if self.config_manager.needs_address_setup():
                raise ValueError("Target address must be configured before starting monitoring")
            
            # Reset stop event and start monitoring thread
            self._stop_event.clear()
            self._monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                name="MonitoringService",
                daemon=True
            )
            
            self._is_running = True
            self._current_status = "starting"
            self._monitoring_thread.start()
            
            # Save initial state
            self._save_monitoring_state()
            
            self.logger.info("Monitoring service started successfully")
            self._emit_event(MonitoringEvent(
                event_type="status_change",
                timestamp=datetime.now(),
                message="Monitoring service started"
            ))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start monitoring service: {e}")
            self._is_running = False
            self._current_status = "error"
            self._emit_event(MonitoringEvent(
                event_type="error",
                timestamp=datetime.now(),
                message=f"Failed to start monitoring: {e}",
                error=e
            ))
            return False
    
    def stop_monitoring(self, timeout: float = 10.0) -> bool:
        """
        Stop the monitoring service gracefully.
        
        Args:
            timeout: Maximum time to wait for monitoring thread to stop
            
        Returns:
            bool: True if monitoring stopped successfully, False otherwise
        """
        if not self._is_running:
            self.logger.warning("Monitoring service is not running")
            return True
        
        try:
            self.logger.info("Stopping monitoring service...")
            self._current_status = "stopping"
            
            # Signal the monitoring thread to stop
            self._stop_event.set()
            
            # Wait for the monitoring thread to finish
            if self._monitoring_thread and self._monitoring_thread.is_alive():
                self._monitoring_thread.join(timeout=timeout)
                
                if self._monitoring_thread.is_alive():
                    self.logger.warning("Monitoring thread did not stop within timeout")
                    return False
            
            self._is_running = False
            self._current_status = "stopped"
            self._monitoring_thread = None
            
            # Save final state
            self._save_monitoring_state()
            
            self.logger.info("Monitoring service stopped successfully")
            self._emit_event(MonitoringEvent(
                event_type="status_change",
                timestamp=datetime.now(),
                message="Monitoring service stopped"
            ))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping monitoring service: {e}")
            self._current_status = "error"
            return False
    
    def restart_monitoring(self) -> bool:
        """
        Restart the monitoring service.
        
        Returns:
            bool: True if restart was successful, False otherwise
        """
        self.logger.info("Restarting monitoring service...")
        
        # Stop current monitoring
        if self._is_running:
            if not self.stop_monitoring():
                return False
        
        # Start monitoring again
        return self.start_monitoring()
    
    def is_running(self) -> bool:
        """Check if the monitoring service is currently running"""
        return self._is_running
    
    def get_status(self) -> str:
        """Get the current status of the monitoring service"""
        return self._current_status
    
    def get_last_check_time(self) -> Optional[datetime]:
        """Get the timestamp of the last check"""
        return self.last_check_time
    
    def get_current_listings(self) -> List[PropertyListing]:
        """Get all current property listings (all scraped listings)"""
        return self.current_listings.copy()
    
    def get_matching_listings(self) -> List[PropertyListing]:
        """Get only the property listings that match the target address"""
        try:
            config = self.config_manager.load_config()
            if not config.target_address or not self.current_listings:
                return []
            
            # Find matches for target address
            matches = self.address_matcher.find_matches(config.target_address, self.current_listings)
            return [match[0] for match in matches]
        except Exception as e:
            self.logger.error(f"Error getting matching listings: {e}")
            return []
    
    def get_found_properties(self) -> Set[str]:
        """Get the set of found property MLS numbers"""
        return self.found_properties.copy()
    
    def _save_monitoring_state(self):
        """Save current monitoring state to persistence"""
        try:
            state = {
                "is_running": self._is_running,
                "current_status": self._current_status,
                "last_check_time": self.last_check_time.isoformat() if self.last_check_time else None,
                "found_properties": list(self.found_properties)
            }
            self.persistence_manager.save_monitoring_state(state)
        except Exception as e:
            self.logger.warning(f"Could not save monitoring state: {e}")
    
    def _save_listings_to_history(self, listings: List[PropertyListing]):
        """Save current listings to history"""
        try:
            self.persistence_manager.save_listings_history(listings)
        except Exception as e:
            self.logger.warning(f"Could not save listings to history: {e}")
    
    def get_listings_history(self) -> List[PropertyListing]:
        """Get historical listings data"""
        try:
            return self.persistence_manager.load_listings_history()
        except Exception as e:
            self.logger.warning(f"Could not load listings history: {e}")
            return []
    
    def perform_immediate_check(self) -> List[PropertyListing]:
        """
        Perform an immediate check for listings (can be called while monitoring is running).
        
        Returns:
            List of property listings that match the target address
            
        Raises:
            Exception: If check fails
        """
        try:
            self.logger.info("Performing immediate check...")
            previous_status = self._current_status
            self._current_status = "checking"
            
            listings = self._fetch_and_check_listings()
            
            # Restore previous status if not stopped
            if not self._stop_event.is_set():
                self._current_status = previous_status
            
            return listings
            
        except Exception as e:
            self.logger.error(f"Immediate check failed: {e}")
            self._current_status = "error"
            self._emit_event(MonitoringEvent(
                event_type="error",
                timestamp=datetime.now(),
                message=f"Immediate check failed: {e}",
                error=e
            ))
            raise
    
    def _monitoring_loop(self):
        """Main monitoring loop that runs in the background thread"""
        self.logger.info("Starting monitoring loop")
        self._current_status = "active"
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while not self._stop_event.is_set():
            try:
                # Load current configuration
                config = self.config_manager.load_config()
                
                # Perform listings check
                self._fetch_and_check_listings()
                
                # Reset error counter on successful check
                consecutive_errors = 0
                
                # Wait for next check with configurable interval
                # Use minimum 0.1 seconds for very short test intervals
                wait_time = max(config.check_interval_minutes, 0.001)  # At least 0.06 seconds
                self._wait_for_next_check(wait_time)
                
            except Exception as e:
                consecutive_errors += 1
                self.logger.error(f"Error in monitoring loop (attempt {consecutive_errors}): {e}")
                
                previous_status = self._current_status
                self._current_status = "error"
                
                self._emit_event(MonitoringEvent(
                    event_type="error",
                    timestamp=datetime.now(),
                    message=f"Monitoring loop error: {e}",
                    error=e
                ))
                
                # Implement exponential backoff for error recovery
                if consecutive_errors >= max_consecutive_errors:
                    self.logger.error(f"Too many consecutive errors ({consecutive_errors}), stopping monitoring")
                    self._emit_event(MonitoringEvent(
                        event_type="error",
                        timestamp=datetime.now(),
                        message=f"Monitoring stopped due to {consecutive_errors} consecutive errors",
                        error=e
                    ))
                    break
                
                # Calculate backoff delay: start with 1 second, double each time, max 60 seconds
                backoff_delay = min(2 ** (consecutive_errors - 1), 60) / 60  # Convert to minutes
                self.logger.info(f"Waiting {backoff_delay * 60:.1f} seconds before retry...")
                self._wait_for_next_check(backoff_delay)
                
                # Restore status if still running
                if not self._stop_event.is_set():
                    self._current_status = "active"
        
        self._current_status = "stopped"
        self.logger.info("Monitoring loop stopped")
    
    def _wait_for_next_check(self, interval_minutes: float):
        """
        Wait for the next check with early termination support.
        
        Args:
            interval_minutes: Number of minutes to wait (can be fractional)
        """
        total_seconds = interval_minutes * 60
        
        # For very short intervals, use smaller sleep increments
        if total_seconds < 1.0:
            sleep_increment = 0.1
            iterations = int(total_seconds / sleep_increment)
        else:
            sleep_increment = 1.0
            iterations = int(total_seconds)
        
        # Wait in small intervals to allow for responsive shutdown
        for _ in range(iterations):
            if self._stop_event.is_set():
                break
            time.sleep(sleep_increment)
    
    def _fetch_and_check_listings(self) -> List[PropertyListing]:
        """
        Fetch current listings and check for target address matches.
        
        Returns:
            List of property listings that match the target address
            
        Raises:
            Exception: If fetching or checking fails
        """
        try:
            # Fetch current listings from website
            self.logger.debug("Fetching listings from website...")
            listings = self.scraper.fetch_listings()
            self.current_listings = listings
            self.last_check_time = datetime.now()
            
            # Update last check time in configuration
            self.config_manager.update_last_check(self.last_check_time)
            
            # Save listings to history
            self._save_listings_to_history(listings)
            
            # Save monitoring state
            self._save_monitoring_state()
            
            # Load current configuration for target address
            config = self.config_manager.load_config()
            
            # Find matches for target address
            matches = self.address_matcher.find_matches(config.target_address, listings)
            current_found_mls = {match[0].mls_number for match in matches}
            
            # Compare with previously found properties to detect changes
            self._process_property_changes(matches, current_found_mls)
            
            self.logger.info(f"Check completed: {len(listings)} total listings, "
                           f"{len(current_found_mls)} target matches")
            
            # Return only the matching listings
            matching_listings = [match[0] for match in matches]
            return matching_listings
            
        except Exception as e:
            self.logger.error(f"Error fetching and checking listings: {e}")
            raise
    
    def _process_property_changes(self, matches: List[tuple], current_found_mls: Set[str]):
        """
        Process changes in found properties (new/removed).
        
        Args:
            matches: List of (PropertyListing, similarity_score) tuples
            current_found_mls: Set of currently found MLS numbers
        """
        # Create lookup for property listings by MLS number
        mls_to_listing = {match[0].mls_number: match[0] for match in matches}
        
        # Detect newly found properties
        new_properties = current_found_mls - self.found_properties
        for mls_number in new_properties:
            try:
                # Add to configuration
                self.config_manager.add_found_listing(mls_number)
                
                # Get the property listing
                property_listing = mls_to_listing.get(mls_number)
                
                self.logger.info(f"New target property found: {mls_number}")
                self._emit_event(MonitoringEvent(
                    event_type="property_found",
                    timestamp=datetime.now(),
                    message=f"Target property found: {property_listing.address if property_listing else mls_number} (MLS: {mls_number})",
                    property_listing=property_listing
                ))
                
            except Exception as e:
                self.logger.error(f"Error processing new property {mls_number}: {e}")
        
        # Detect removed properties
        removed_properties = self.found_properties - current_found_mls
        for mls_number in removed_properties:
            try:
                # Remove from configuration
                self.config_manager.remove_found_listing(mls_number)
                
                self.logger.info(f"Target property removed: {mls_number}")
                self._emit_event(MonitoringEvent(
                    event_type="property_removed",
                    timestamp=datetime.now(),
                    message=f"Target property removed: {mls_number}",
                    property_listing=None  # We don't have the listing data for removed properties
                ))
                
            except Exception as e:
                self.logger.error(f"Error processing removed property {mls_number}: {e}")
        
        # Update internal state
        self.found_properties = current_found_mls
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - ensure monitoring is stopped"""
        if self._is_running:
            self.stop_monitoring()