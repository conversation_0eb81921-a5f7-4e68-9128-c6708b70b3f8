"""
Unit tests for the NotificationManager class.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from shared.notification_manager import (
    NotificationManager, 
    NotificationMessage, 
    NotificationType
)
from shared.models import PropertyListing


class TestNotificationManager:
    """Test cases for NotificationManager"""
    
    def setup_method(self):
        """Set up test fixtures before each test method"""
        self.notification_manager = NotificationManager("Test App")
        
        # Create a test property listing
        self.test_listing = PropertyListing(
            address="123 Test Street",
            price="$500,000",
            mls_number="TEST123",
            description="Beautiful test property with great features and amenities.",
            bedrooms=3,
            bathrooms=2,
            square_footage="1,500 sq ft",
            lot_size="0.25 acres",
            year_built="2020"
        )
    
    def test_initialization(self):
        """Test NotificationManager initialization"""
        manager = NotificationManager("Custom App")
        
        assert manager.app_name == "Custom App"
        assert manager.web_notifications_enabled is True
        assert len(manager._websocket_callbacks) == 0
        
        # Test default app name
        default_manager = NotificationManager()
        assert default_manager.app_name == "Real Estate Monitor"
    
    def test_websocket_callback_management(self):
        """Test adding and removing WebSocket callbacks"""
        callback1 = Mock()
        callback2 = Mock()
        
        # Test adding callbacks
        self.notification_manager.add_websocket_callback(callback1)
        self.notification_manager.add_websocket_callback(callback2)
        
        assert len(self.notification_manager._websocket_callbacks) == 2
        assert callback1 in self.notification_manager._websocket_callbacks
        assert callback2 in self.notification_manager._websocket_callbacks
        
        # Test adding duplicate callback (should not add twice)
        self.notification_manager.add_websocket_callback(callback1)
        assert len(self.notification_manager._websocket_callbacks) == 2
        
        # Test removing callbacks
        self.notification_manager.remove_websocket_callback(callback1)
        assert len(self.notification_manager._websocket_callbacks) == 1
        assert callback1 not in self.notification_manager._websocket_callbacks
        assert callback2 in self.notification_manager._websocket_callbacks
        
        # Test removing non-existent callback (should not raise error)
        self.notification_manager.remove_websocket_callback(callback1)
        assert len(self.notification_manager._websocket_callbacks) == 1
    
    @patch('shared.notification_manager.desktop_notification')
    def test_notify_property_found(self, mock_desktop_notification):
        """Test property found notification"""
        # Mock WebSocket callback
        websocket_callback = Mock()
        self.notification_manager.add_websocket_callback(websocket_callback)
        
        # Enable desktop notifications for testing
        self.notification_manager.desktop_notifications_enabled = True
        
        # Send notification
        self.notification_manager.notify_property_found(self.test_listing)
        
        # Verify WebSocket callback was called
        websocket_callback.assert_called_once()
        call_args = websocket_callback.call_args[0][0]
        
        assert call_args["type"] == "notification"
        assert call_args["notification_type"] == "property_found"
        assert call_args["title"] == "🏠 Target Property Found!"
        assert "123 Test Street" in call_args["message"]
        assert "$500,000" in call_args["message"]
        assert "TEST123" in call_args["message"]
        assert call_args["property"]["address"] == "123 Test Street"
        assert call_args["property"]["price"] == "$500,000"
        
        # Verify desktop notification was called
        mock_desktop_notification.notify.assert_called_once()
        desktop_call_args = mock_desktop_notification.notify.call_args[1]
        assert desktop_call_args["title"] == "🏠 Target Property Found!"
        assert desktop_call_args["app_name"] == "Test App"
        assert desktop_call_args["timeout"] == 10
    
    @patch('shared.notification_manager.desktop_notification')
    def test_notify_property_removed(self, mock_desktop_notification):
        """Test property removed notification"""
        # Mock WebSocket callback
        websocket_callback = Mock()
        self.notification_manager.add_websocket_callback(websocket_callback)
        
        # Enable desktop notifications for testing
        self.notification_manager.desktop_notifications_enabled = True
        
        # Send notification
        self.notification_manager.notify_property_removed("TEST123", "123 Test Street")
        
        # Verify WebSocket callback was called
        websocket_callback.assert_called_once()
        call_args = websocket_callback.call_args[0][0]
        
        assert call_args["type"] == "notification"
        assert call_args["notification_type"] == "property_removed"
        assert call_args["title"] == "🏠 Target Property Removed"
        assert "123 Test Street" in call_args["message"]
        assert "TEST123" in call_args["message"]
        assert call_args["data"]["mls_number"] == "TEST123"
        assert call_args["data"]["address"] == "123 Test Street"
        
        # Verify desktop notification was called
        mock_desktop_notification.notify.assert_called_once()
    
    @patch('shared.notification_manager.desktop_notification')
    def test_notify_status_update(self, mock_desktop_notification):
        """Test status update notification"""
        # Mock WebSocket callback
        websocket_callback = Mock()
        self.notification_manager.add_websocket_callback(websocket_callback)
        
        # Enable desktop notifications for testing
        self.notification_manager.desktop_notifications_enabled = True
        
        # Send notification
        self.notification_manager.notify_status_update("Monitoring started", "success")
        
        # Verify WebSocket callback was NOT called (desktop only for status)
        websocket_callback.assert_not_called()
        
        # Verify desktop notification was called
        mock_desktop_notification.notify.assert_called_once()
        desktop_call_args = mock_desktop_notification.notify.call_args[1]
        assert desktop_call_args["title"] == "✅ Success"
        assert desktop_call_args["message"] == "Monitoring started"
    
    @patch('shared.notification_manager.desktop_notification')
    def test_notify_error(self, mock_desktop_notification):
        """Test error notification"""
        # Mock WebSocket callback
        websocket_callback = Mock()
        self.notification_manager.add_websocket_callback(websocket_callback)
        
        # Enable desktop notifications for testing
        self.notification_manager.desktop_notifications_enabled = True
        
        # Test error notification with exception
        test_exception = ValueError("Test error")
        self.notification_manager.notify_error("Something went wrong", test_exception)
        
        # Verify WebSocket callback was called
        websocket_callback.assert_called_once()
        call_args = websocket_callback.call_args[0][0]
        
        assert call_args["type"] == "notification"
        assert call_args["notification_type"] == "error"
        assert call_args["title"] == "⚠️ Monitoring Error"
        assert "Something went wrong" in call_args["message"]
        assert "Test error" in call_args["message"]
        assert call_args["data"]["error_type"] == "ValueError"
        
        # Verify desktop notification was called
        mock_desktop_notification.notify.assert_called_once()
    
    def test_format_property_found_message(self):
        """Test property found message formatting"""
        message = self.notification_manager._format_property_found_message(self.test_listing)
        
        assert "Address: 123 Test Street" in message
        assert "Price: $500,000" in message
        assert "MLS: TEST123" in message
        assert "Bedrooms: 3" in message
        assert "Bathrooms: 2" in message
        assert "Size: 1,500 sq ft" in message
        assert "Description: Beautiful test property" in message
        
        # Test with minimal property data
        minimal_listing = PropertyListing(
            address="456 Simple St",
            price="$300,000",
            mls_number="SIMPLE456",
            description=""
        )
        
        minimal_message = self.notification_manager._format_property_found_message(minimal_listing)
        assert "Address: 456 Simple St" in minimal_message
        assert "Price: $300,000" in minimal_message
        assert "MLS: SIMPLE456" in minimal_message
        assert "Bedrooms:" not in minimal_message
        assert "Description:" not in minimal_message
    
    def test_format_property_removed_message(self):
        """Test property removed message formatting"""
        # Test with address
        message_with_address = self.notification_manager._format_property_removed_message(
            "TEST123", "123 Test Street"
        )
        assert "123 Test Street" in message_with_address
        assert "TEST123" in message_with_address
        assert "removed from listings" in message_with_address
        
        # Test without address
        message_without_address = self.notification_manager._format_property_removed_message("TEST123")
        assert "TEST123" in message_without_address
        assert "removed from listings" in message_without_address
        assert "123 Test Street" not in message_without_address
    
    def test_format_error_message(self):
        """Test error message formatting"""
        # Test with exception
        test_exception = ConnectionError("Network timeout")
        message_with_exception = self.notification_manager._format_error_message(
            "Failed to connect", test_exception
        )
        assert "Failed to connect" in message_with_exception
        assert "Network timeout" in message_with_exception
        
        # Test without exception
        message_without_exception = self.notification_manager._format_error_message("Simple error")
        assert message_without_exception == "Simple error"
    
    def test_get_status_title(self):
        """Test status title generation"""
        assert self.notification_manager._get_status_title("info") == "ℹ️ Status Update"
        assert self.notification_manager._get_status_title("warning") == "⚠️ Warning"
        assert self.notification_manager._get_status_title("error") == "❌ Error"
        assert self.notification_manager._get_status_title("success") == "✅ Success"
        assert self.notification_manager._get_status_title("unknown") == "📢 Notification"
    
    def test_websocket_callback_error_handling(self):
        """Test error handling in WebSocket callbacks"""
        # Create a callback that raises an exception
        failing_callback = Mock(side_effect=Exception("Callback error"))
        working_callback = Mock()
        
        self.notification_manager.add_websocket_callback(failing_callback)
        self.notification_manager.add_websocket_callback(working_callback)
        
        # Send notification - should not raise exception despite failing callback
        self.notification_manager.notify_property_found(self.test_listing)
        
        # Verify both callbacks were called
        failing_callback.assert_called_once()
        working_callback.assert_called_once()
    
    @patch('shared.notification_manager.DESKTOP_NOTIFICATIONS_AVAILABLE', False)
    def test_desktop_notifications_unavailable(self):
        """Test behavior when desktop notifications are unavailable"""
        manager = NotificationManager()
        
        # Should be disabled when not available
        assert manager.desktop_notifications_enabled is False
        
        # Trying to enable should not work
        manager.set_desktop_notifications_enabled(True)
        assert manager.desktop_notifications_enabled is False
        
        # Should report as not available
        assert manager.is_desktop_notifications_available() is False
    
    def test_notification_settings(self):
        """Test notification settings management"""
        # Test getting settings
        settings = self.notification_manager.get_notification_settings()
        
        assert "desktop_notifications_enabled" in settings
        assert "desktop_notifications_available" in settings
        assert "web_notifications_enabled" in settings
        assert "app_name" in settings
        assert settings["app_name"] == "Test App"
        
        # Test setting desktop notifications
        self.notification_manager.set_desktop_notifications_enabled(False)
        assert self.notification_manager.desktop_notifications_enabled is False
        
        self.notification_manager.set_desktop_notifications_enabled(True)
        # This depends on whether plyer is available in test environment
        
        # Test setting web notifications
        self.notification_manager.set_web_notifications_enabled(False)
        assert self.notification_manager.web_notifications_enabled is False
        
        self.notification_manager.set_web_notifications_enabled(True)
        assert self.notification_manager.web_notifications_enabled is True
    
    def test_websocket_notification_data_structure(self):
        """Test WebSocket notification data structure"""
        websocket_callback = Mock()
        self.notification_manager.add_websocket_callback(websocket_callback)
        
        # Send property found notification
        self.notification_manager.notify_property_found(self.test_listing)
        
        # Verify data structure
        call_args = websocket_callback.call_args[0][0]
        
        # Check required fields
        required_fields = ["type", "notification_type", "title", "message", "timestamp", "data"]
        for field in required_fields:
            assert field in call_args
        
        # Check property data structure
        assert "property" in call_args
        property_data = call_args["property"]
        
        property_fields = [
            "address", "price", "mls_number", "description", "square_footage",
            "bedrooms", "bathrooms", "lot_size", "year_built", "image_url",
            "listing_url", "first_seen"
        ]
        for field in property_fields:
            assert field in property_data
        
        # Verify timestamp is ISO format
        timestamp_str = call_args["timestamp"]
        # Should not raise exception when parsing
        datetime.fromisoformat(timestamp_str.replace('Z', '+00:00') if timestamp_str.endswith('Z') else timestamp_str)
    
    @patch('shared.notification_manager.desktop_notification')
    def test_desktop_notification_message_truncation(self, mock_desktop_notification):
        """Test that long messages are truncated for desktop notifications"""
        self.notification_manager.desktop_notifications_enabled = True
        
        # Create a property with a very long description
        long_description = "This is a very long description that exceeds the typical character limit for desktop notifications. " * 5
        long_listing = PropertyListing(
            address="123 Long Description Street",
            price="$600,000",
            mls_number="LONG123",
            description=long_description
        )
        
        self.notification_manager.notify_property_found(long_listing)
        
        # Verify desktop notification was called with truncated message
        mock_desktop_notification.notify.assert_called_once()
        desktop_call_args = mock_desktop_notification.notify.call_args[1]
        
        # Message should be truncated to 200 characters or less
        assert len(desktop_call_args["message"]) <= 200
        if len(desktop_call_args["message"]) == 200:
            assert desktop_call_args["message"].endswith("...")
    
    def test_notification_message_dataclass(self):
        """Test NotificationMessage dataclass"""
        message = NotificationMessage(
            notification_type=NotificationType.PROPERTY_FOUND,
            title="Test Title",
            message="Test Message",
            timestamp=datetime.now(),
            property_listing=self.test_listing,
            data={"key": "value"}
        )
        
        assert message.notification_type == NotificationType.PROPERTY_FOUND
        assert message.title == "Test Title"
        assert message.message == "Test Message"
        assert message.property_listing == self.test_listing
        assert message.data == {"key": "value"}
        assert isinstance(message.timestamp, datetime)
    
    def test_notification_type_enum(self):
        """Test NotificationType enum"""
        assert NotificationType.PROPERTY_FOUND.value == "property_found"
        assert NotificationType.PROPERTY_REMOVED.value == "property_removed"
        assert NotificationType.STATUS_UPDATE.value == "status_update"
        assert NotificationType.ERROR.value == "error"


if __name__ == '__main__':
    pytest.main([__file__])